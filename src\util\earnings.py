import requests
import datetime

#todays date in format 20250117
tdy = datetime.datetime.today().strftime('%Y%m%d')

url = "https://www.earningswhispers.com/api/caldata/"+tdy

payload = {}
headers = {
  'accept': 'application/json, text/javascript, */*; q=0.01',
  'accept-language': 'en-US,en;q=0.9',
  'dnt': '1',
  'priority': 'u=1, i',
  'referer': 'https://www.earningswhispers.com/calendar',
  'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'sec-gpc': '1',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-requested-with': 'XMLHttpRequest'
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.json())
from tabulate import tabulate
print(tabulate(response.json(), tablefmt='fancy_grid', headers='keys', showindex=False))
