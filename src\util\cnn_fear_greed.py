import matplotlib.pyplot as plt
from datetime import datetime
from common.http_session import get_session

# Fetching data from the API
url = "https://production.dataviz.cnn.io/index/fearandgreed/graphdata"

headers = {
   'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
}

def CNNGreedFear():
   # Create a session with proper SSL certificate handling
   session = get_session()

   # Make the request with our custom session
   response = session.get(url, headers=headers)

   # Check if the request was successful
   if response.status_code == 200:
       data = response.json()

       # Extracting historical data
       historical_data = data["fear_and_greed_historical"]["data"]
       x_values = [datetime.fromtimestamp(entry["x"] / 1000) for entry in historical_data]
       y_values = [entry["y"] for entry in historical_data]

       # Plotting
       plt.plot(x_values, y_values, marker='o', linestyle='-')

       # Adding labels and title
       plt.xlabel('Timestamp')
       plt.ylabel('Score')
       plt.title('Fear and Greed Index Historical Data')
       plt.xticks(rotation=45)  # Rotate x-axis labels for better readability

       # Displaying the plot
       plt.tight_layout()  # Adjust layout to prevent clipping of labels
       plt.show()
   else:
       print("Failed to retrieve data. Status code:", response.status_code)