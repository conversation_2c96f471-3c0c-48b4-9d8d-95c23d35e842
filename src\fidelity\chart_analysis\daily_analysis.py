from util.scrapper import downloadChartsFidelity # Corrected absolute import from root
import pandas as pd

def convergence(screener):
    screener = screener.dropna(subset=['% Price off 20 Day SMA'])
    screener = screener.sort_values(by='% Price off 20 Day SMA')
    screener['Convergence'] = screener[['% Price off 50 Day SMA', '% Price off 20 Day SMA','% Price off 10 Day SMA']].diff().abs().sum(axis=1)
    df_sorted = screener.sort_values(by='Convergence', ascending=True)
    df_sorted.to_excel("sorted_screener_convg.xlsx")
    return screener

def groupAndFilter(df):

    # Remove rows where 'Security Type' is 'Common Stock (REIT)'
    df = df[df['Security Type'] != 'Common Stock (REIT)']

    # Get the count of occurrences within each 'Sector'
    sector_counts = df['Sector'].value_counts().reset_index(name='Sector_Count')
    sector_counts.columns = ['Sector', 'Sector_Count']

    # Merge the count back into the original DataFrame
    df = pd.merge(df, sector_counts, on='Sector')

    # Sort by 'Sector_Count' in descending order, then by 'Industry' and 'Sub-Industry' in ascending order
    df = df.sort_values(by=['Sector_Count', 'Industry', 'Sub-Industry'], ascending=[False, False, False])

    # Drop the 'Sector_Count' column if you no longer need it
    df = df.drop('Sector_Count', axis=1)

    df = df.drop_duplicates(subset='Symbol')

    df.to_excel("sorted_screener.xlsx")

    return df



def chartAnalysis():

    df = pd.DataFrame();

    df = pd.read_excel("screener_results.xls")

    #screener = groupAndFilter(df)

    #df_sorted = convergence(screener)

    df_sorted = df

    tickers = df_sorted["Symbol"].to_list()


    stock_symbols = [entry for entry in tickers if isinstance(entry, str) and len(entry) <= 5]


    print(stock_symbols)

    print(len(stock_symbols))

    downloadChartsFidelity(stock_symbols,"fidelity_daily")



chartAnalysis();
