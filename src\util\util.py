# include all util methods here
import datetime
import pandas as pd
from indicators.calc_ATR import calcATR
import numpy as np
from datetime import date
from datetime import timedelta

from util.TTM import calculate_squeeze_momentum

#method to normalize data frames when comparing tickers
def normalizeDF(ticker1Df, ticker2Df):
    ticker1DFCount  = ticker1Df.shape[0]
    ticker2DFCount  = ticker2Df.shape[0]
    ticker1Df.sort_values(by='Date', ascending=False,inplace=True)
    ticker2Df.sort_values(by='Date', ascending=False,inplace=True)

    if(ticker1DFCount == ticker2DFCount):
        return ticker1Df,ticker2Df
    elif(ticker1DFCount < ticker2DFCount):
        return ticker1Df[:ticker1DFCount],ticker2Df[:ticker1DFCount]
    else:
        return ticker1Df[:ticker2DFCount],ticker2Df[:ticker2DFCount]



def getEWM(df, period, key):
    return round(df[key].ewm(span=period, adjust=False).mean(),2)


def getWM(df, period, key):
    return round(df[key].rolling(period).mean() ,2)


# def calcSlope(df,key,lookbackperiod):
#      df['slope_'+ key]=(df[key].diff(periods = -1 * lookbackperiod))/lookbackperiod;
#      return df

def calcExtension(df, key):
    """
    Calculate extension as percentage difference between Close and the moving average.

    Args:
        df: DataFrame containing price data
        key: Moving average column name (e.g., '20', '50', '100', '200')

    Returns:
        DataFrame with extension column added
    """
    try:
        # Check if the DataFrame has MultiIndex columns
        if isinstance(df.columns, pd.MultiIndex):
            # Get the ticker from the MultiIndex
            ticker = df.columns.get_level_values('Ticker')[0]

            # Extract values directly as numpy arrays for better performance
            close_values = df[('Close', ticker)].values
            ma_values = df[key].values

            # Calculate the extension as a percentage using vectorized operations
            # This is more efficient than Series operations
            with np.errstate(divide='ignore', invalid='ignore'):  # Ignore division by zero warnings
                ext_values = 100 * (close_values - ma_values) / ma_values

            # Replace any NaN or inf values with 0
            ext_values = np.nan_to_num(ext_values, nan=0.0, posinf=0.0, neginf=0.0)

            # Add the result as a new column
            df['ext_'+ key] = ext_values
        else:
            # Regular DataFrame - use pandas operations for simplicity
            close_series = df['Close']
            ma_series = df[key]

            # Calculate the extension as a percentage
            df['ext_'+ key] = 100 * (close_series - ma_series) / ma_series.replace(0, np.nan).fillna(close_series)
    except Exception as e:
        print(f"Warning: Extension calculation for {key} failed: {e}")
        # Use placeholder if calculation fails
        df['ext_'+ key] = 0

    return df

def calcSlope(df, key, lookbackperiod):
    """Calculate the slope of a given column over a lookback period.

    This implementation uses numpy arrays to avoid DataFrame operation issues with newer yfinance.
    """
    slopes = []
    # Convert to numpy array for consistent behavior
    values = df[key].to_numpy()

    for i in range(lookbackperiod, len(df)):
        y = values[i - lookbackperiod:i]  # Get values for the lookback period
        x = np.arange(lookbackperiod)     # Index values for regression
        slope = np.polyfit(x, y, 1)[0]    # Linear regression slope
        slopes.append(slope)

    # Pad initial values with NaN
    result = [np.nan] * lookbackperiod + slopes
    # Ensure the result has the same length as the dataframe
    if len(result) > len(df):
        result = result[:len(df)]
    elif len(result) < len(df):
        result = result + [np.nan] * (len(df) - len(result))

    df['slope_'+ key] = result

def loadMovingAverages(df):

    ema = [5,8,10,20,30]
    sma = [50,100,150,200]

    for val in ema:
        df[str(val)] = getEWM(df, val,'Close')

    for val in sma:
        df[str(val)] = getWM(df, val,'Close')

    # df['5'] = getWM(df, 10,'Close')
    # df['9'] = getEWM(df, 10,'Close')
    # df['10'] = getEWM(df, 10,'Close')
    # df['20'] = getEWM(df, 20,'Close')
    # df['50'] = getWM(df, 50,'Close')
    # df['200'] = getWM(df, 200,'Close')

      # Convert volume to millions for better readability

    df['ATR'] = calcATR(df,21)
    df['20Volume'] = getWM(df,20,'Volume')

    # Calculate $VolumeM as (20-day volume average / 1 million) * Close price
    # Handle MultiIndex columns if present
    try:
        if isinstance(df.columns, pd.MultiIndex):
            # Get the ticker from the MultiIndex
            ticker = df.columns.get_level_values('Ticker')[0]

            # Extract values directly as numpy arrays for better performance
            vol_values = df['20Volume'].values
            close_values = df[('Close', ticker)].values

            # Calculate dollar volume in millions using vectorized operations
            # This is more efficient than Series operations
            dollar_volume = (vol_values / 1e6) * close_values

            # Add the result as a new column
            df['$VolumeM'] = dollar_volume
        else:
            # Regular DataFrame - use numpy operations for better performance
            vol_values = df['20Volume'].values
            close_values = df['Close'].values

            # Calculate dollar volume in millions
            dollar_volume = (vol_values / 1e6) * close_values
            df['$VolumeM'] = dollar_volume
    except Exception as e:
        print(f"Warning: $VolumeM calculation failed: {e}")
        # Use simplified version if calculation fails
        df['$VolumeM'] = df['20Volume'] / 1e6

    # Calculate ADR for a 20-day period
    try:
        # Handle MultiIndex columns if present
        if isinstance(df.columns, pd.MultiIndex):
            # Get the High and Low using the MultiIndex
            ticker = df.columns.get_level_values('Ticker')[0]
            high_series = df[('High', ticker)]
            low_series = df[('Low', ticker)]

            # Calculate ADRP
            df['ADRP'] = 100 * ((high_series / low_series).rolling(window=20).mean() - 1)
        else:
            # Regular DataFrame
            df['ADRP'] = 100 * ((df['High'] / df['Low']).rolling(window=20).mean() - 1)
    except Exception as e:
        print(f"Warning: ADRP calculation failed: {e}")
        # Use placeholder value if calculation fails
        df['ADRP'] = 0

    # Calculate Up/Down Volume Ratio (UDRatio)
    try:
        # Handle MultiIndex columns if present
        if isinstance(df.columns, pd.MultiIndex):
            # Get the Close and Volume using the MultiIndex
            ticker = df.columns.get_level_values('Ticker')[0]
            close_series = df[('Close', ticker)]
            volume_series = df[('Volume', ticker)]

            # Calculate Up/Down Volume
            df['UpVolume'] = np.where(close_series > close_series.shift(1), volume_series, 0)
            df['DownVolume'] = np.where(close_series < close_series.shift(1), volume_series, 0)
        else:
            # Regular DataFrame
            df['UpVolume'] = np.where(df['Close'] > df['Close'].shift(1), df['Volume'], 0)
            df['DownVolume'] = np.where(df['Close'] < df['Close'].shift(1), df['Volume'], 0)

        # Calculate the ratio
        df['UDRatio'] = df['UpVolume'].rolling(window=50).sum() / df['DownVolume'].rolling(window=50).sum()
    except Exception as e:
        print(f"Warning: UDRatio calculation failed: {e}")
        # Use placeholder values if calculation fails
        df['UpVolume'] = 0
        df['DownVolume'] = 0
        df['UDRatio'] = 1  # Neutral value

    # Calculate TTM (TTM Squeeze Momentum)
    try:
        # Handle MultiIndex columns if present
        if isinstance(df.columns, pd.MultiIndex):
            # Get the High, Low, Close using the MultiIndex
            ticker = df.columns.get_level_values('Ticker')[0]

            # Extract the required columns more efficiently
            high_values = df[('High', ticker)].values
            low_values = df[('Low', ticker)].values
            close_values = df[('Close', ticker)].values

            # Create a temporary DataFrame with the required columns
            # Using a dictionary with numpy arrays is more efficient
            temp_df = pd.DataFrame({
                'High': high_values,
                'Low': low_values,
                'Close': close_values
            }, index=df.index)

            # Calculate TTM
            df['TTM'] = calculate_squeeze_momentum(temp_df)
        else:
            # For regular DataFrame, we can pass it directly
            # This avoids unnecessary data copying
            df['TTM'] = calculate_squeeze_momentum(df)
    except Exception as e:
        print(f"Warning: TTM calculation failed: {e}")
        # Use default value if calculation fails
        df['TTM'] = False

    calcSlope(df,'5',14);
    calcSlope(df,'10',14);
    calcSlope(df,'20',14);
    calcSlope(df,'50',20);
    calcSlope(df,'150',20);
    calcSlope(df,'200',20);

    calcSlope(df, 'UDRatio', 50)


    df.sort_values(by='Date', ascending=False, inplace=True)


    # Calculate extension values using the calcExtension function
    calcExtension(df, '20')
    calcExtension(df, '50')
    calcExtension(df, '100')
    calcExtension(df, '200')
    # slope_deg = (math.atan(slope) * 180) / math.pi
    # print(df)



    return df

def loadMovingAveragesV2(df):
    df['10'] = getEWM(df, 10,'Adj Close')
    df['20'] = getEWM(df, 20,'Adj Close')
    df['50'] = getWM(df, 50,'Adj Close')
    df['100'] = getWM(df, 50,'Adj Close')
    df['200'] = getWM(df, 200,'Adj Close')
    df['ATR'] = calcATR(df,21)
    df['20Volume'] = getWM(df,20,'Volume')
    df.sort_values(by='Date', ascending=False, inplace=True)
    #print(df)
    return df

def calcSdWeight(val,period):
    standard_deviation = np.std(val[:period], ddof=1)
    #print(val[:200])
    mean_val = np.mean(val[:period])
    #print(val[0],mean_val,standard_deviation)
    return assignWeight(val, mean_val, standard_deviation)

def assignWeight(val, mean_val, standard_deviation):
    #print(val)
    #print(mean_val - standard_deviation)
    if mean_val - (1*standard_deviation) <= val[0] <= mean_val + (1*standard_deviation):
        #print('Within 1 Deviation')
        return 1
    elif mean_val - (2*standard_deviation) <= val[0] <= mean_val + (2*standard_deviation):
        #print('Within 1 Deviation')
        return 2
    elif mean_val - (3*standard_deviation) <= val[0] <= mean_val + (3*standard_deviation):
        #print('Within 1 Deviation')
        return 3

    else:
        return 4


def remove_all_occurrences(list_obj, value):
    while value in list_obj:
        list_obj.remove(value)
    return list_obj


# def permutationGen():
#     # Get all permutations of [1, 2, 3]
#     const = 'ZAE1BJPREM'
#     keys = []
#     val = 'F8FZY3'
#     val = list(val)
#     print(val)
#     perm = permutations(val)
#     # Print the obtained permutations
#     for i in list(perm):
#         keys.append(const+''.join(i))
#     return keys
# permutationGen()
#
#print(remove_all_occurrences([1,1,1,.3],1) )

def findExchange(ticker):
    # Use relative path for exchange files
    import os
    nasdaq_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src/data', 'exchange', 'nasdaqlisted.txt')
    nyse_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src/data', 'exchange', 'otherlisted.txt')

    nasdaq = pd.read_csv(nasdaq_path, sep="|")
    nyse = pd.read_csv(nyse_path, sep="|")
    exchange = ''
    if ticker in nasdaq.values:
       exchange = 'NASDAQ'
    elif ticker in nyse.values:
        exchange = 'NYSE'
    else:
        exchange = 'NYSEAMERICAN'
    return exchange

def findLastWed():
    today = date.today()
    offset = (today.weekday() - 2) % 7
    last_wednesday = today - timedelta(days=offset)
    if date.today().weekday() == 2:
        return today.strftime("%Y-%m-%d")
    else:
        return last_wednesday.strftime("%Y-%m-%d")
    #print(t.strftime("%d/%m/%Y was a %A."))
#print(findLastWed())


def time_until_end_of_day(dt=None):
    if dt is None:
        dt = datetime.datetime.now()
    return ((24 - dt.hour - 1) * 60 * 60) + ((60 - dt.minute - 1) * 60) + (60 - dt.second)

#print(time_until_end_of_day())

def read_and_filter_csv(percentile_threshold=78):
    # Read the CSV file using relative path
    import os
    # Try both possible paths for rs_stocks.csv
    rs_data_path_1 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'rs_data', 'rs_stocks.csv')
    rs_data_path_2 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'data', 'rs_data', 'rs_stocks.csv')

    # Check which path exists
    if os.path.exists(rs_data_path_1):
        rs_data_path = rs_data_path_1
    elif os.path.exists(rs_data_path_2):
        rs_data_path = rs_data_path_2
    else:
        # If neither path exists, try a direct path
        rs_data_path = 'src/data/rs_data/rs_stocks.csv'
        if not os.path.exists(rs_data_path):
            raise FileNotFoundError(f"Could not find rs_stocks.csv in any of the expected locations: {rs_data_path_1}, {rs_data_path_2}, or {rs_data_path}")

    print(f"Using RS data path: {rs_data_path}")
    df = pd.read_csv(rs_data_path)
    # Filter the DataFrame where Percentile > percentile_threshold
    filtered_df = df[df['Percentile'] > percentile_threshold]
    return filtered_df
