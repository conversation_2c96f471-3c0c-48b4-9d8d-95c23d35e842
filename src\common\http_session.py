import os
import warnings
from curl_cffi import requests as curl_requests
import requests as standard_requests

def get_session(pem_path=None, use_standard_requests=False, disable_warnings=True):
    """
    Return a session with proper SSL certificate verification.

    Args:
        pem_path (str, optional): Path to the CA bundle file. If None, uses environment variables.
        use_standard_requests (bool, optional): If True, use standard requests library instead of curl_cffi.
        disable_warnings (bool, optional): If True, disable SSL verification warnings when verification is disabled.

    Returns:
        Session object: Either a curl_cffi Session or a standard requests Session.
    """
    # Get certificate path from environment variables if not provided
    if pem_path is None:
        pem_path = os.environ.get('SSL_CERT_FILE') or os.environ.get('REQUESTS_CA_BUNDLE') or os.environ.get('CURL_CA_BUNDLE')

    # Check if the certificate file exists
    cert_exists = pem_path and os.path.exists(pem_path)

    if use_standard_requests:
        # Use standard requests library
        session = standard_requests.Session()

        if cert_exists:
            # Use the certificate file
            session.verify = pem_path
        else:
            # Disable SSL verification if certificate file doesn't exist
            if disable_warnings:
                warnings.filterwarnings('ignore', message='Unverified HTTPS request')
            session.verify = False

        return session
    else:
        # Use curl_cffi with Chrome impersonation
        try:
            return curl_requests.Session(impersonate="chrome", verify=pem_path if cert_exists else False)
        except Exception as e:
            # Fall back to standard requests if curl_cffi fails
            print(f"Warning: curl_cffi failed ({str(e)}), falling back to standard requests")
            session = standard_requests.Session()

            if cert_exists:
                session.verify = pem_path
            else:
                if disable_warnings:
                    warnings.filterwarnings('ignore', message='Unverified HTTPS request')
                session.verify = False

            return session
