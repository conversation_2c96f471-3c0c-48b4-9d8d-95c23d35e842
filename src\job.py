from jobs.images import dwnloadImg, open_webpage, showImg
from util.scrapper import NAAIMExp, getPCRatios
from util.clean_up import cleanUpPCData
from util.cnn_fear_greed import CNNGreedFear
from util.rs_rating_files import download_rs_files
from util.cryp_free_greed import plot as cry_free_greed_plot

def main():
    try:
        print("Starting download of RS files...")
        download_rs_files()
        print("RS files downloaded successfully.")
    except Exception as e:
        print(f"An error occurred while downloading RS files: {e}")

    try:
        print("Running Crypto Fear and Greed...")
        cry_free_greed_plot()
        print("Crypto Fear and Greed executed.")
    except Exception as e:
        print(f"An error occurred while Crypto Fear and Greed: {e}")


    try:
        print("Running NAAIMExp function...")
        NAAIMExp()
        print("NAAIMExp function executed.")
    except Exception as e:
        print(f"An error occurred while executing NAAIMExp: {e}")

    try:
        print("Running CNN Greed Fear analysis...")
        CNNGreedFear()
        print("CNN Greed Fear analysis completed.")
    except Exception as e:
        print(f"An error occurred during CNN Greed Fear analysis: {e}")

    try:
        print("Opening the webpage...")
        open_webpage("https://www.forexfactory.com/calendar?week=this")
        print("Webpage opened successfully.")
    except Exception as e:
        print(f"An error occurred while opening the webpage: {e}")

    try:
        # One day delayed data processing
        print("Getting PCR ratios...")
        getPCRatios()
        print("PCR ratios obtained.")
    except Exception as e:
        print(f"An error occurred while getting PCR ratios: {e}")

    try:
        print("Cleaning up PC data...")
        cleanUpPCData()
        print("PC data cleaned up successfully.")
    except Exception as e:
        print(f"An error occurred during PC data cleanup: {e}")

    try:
        print("Starting image download...")
        dwnloadImg()
        print("Image download completed.")
    except Exception as e:
        print(f"An error occurred during image download: {e}")

    try:
        print("Displaying image...")
        showImg()
        print("Image displayed successfully.")
    except Exception as e:
        print(f"An error occurred while displaying image: {e}")

if __name__ == "__main__":
    main()