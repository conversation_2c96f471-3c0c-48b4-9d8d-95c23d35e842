"""
Unit test for Robinhood news functionality in ticker_analyzer.data_fetcher module.
Tests the ability to fetch news from Robinhood for specific tickers.
"""

import sys
import os
import unittest
from io import StringIO
import contextlib
from datetime import datetime, timezone

# Add src directory to Python path
sys.path.append('src')

# Import the function to test
from ticker_analyzer.data_fetcher import print_catalyst_news, login_robinhood
import robin_stocks.robinhood as r

def test_robinhood_login():
    """Test Robinhood login functionality."""
    print("\n=== Testing Robinhood Login ===")

    # Check if environment variables are set
    rh_un = os.environ.get('RH_UN')
    rh_pw = os.environ.get('RH_PW')

    if not rh_un or not rh_pw:
        print("WARNING: Robinhood credentials not found in environment variables.")
        return False

    # Try to login
    try:
        login_result = login_robinhood()
        if login_result:
            print("SUCCESS: Robinhood login successful")
            return True
        else:
            print("WARNING: Robinhood login failed")
            return False
    except Exception as e:
        print(f"ERROR: Robinhood login failed with exception: {e}")
        return False

def test_direct_robinhood_news(ticker_symbol):
    """Test direct Robinhood news retrieval for a specific ticker."""
    print(f"\n=== Testing Direct Robinhood News for {ticker_symbol} ===")

    try:
        # Get today's date in ISO format
        today = datetime.now(timezone.utc).date().isoformat()

        # Get news directly from Robinhood API
        all_news = r.stocks.get_news(ticker_symbol)

        if all_news and isinstance(all_news, list):
            print(f"SUCCESS: Found {len(all_news)} total news items for {ticker_symbol}")

            # Filter for today's news
            todays_news = [news for news in all_news if news.get('published_at', '').startswith(today)]

            if todays_news:
                print(f"SUCCESS: Found {len(todays_news)} news items published today for {ticker_symbol}")
            else:
                print(f"NOTE: No news published today for {ticker_symbol}")

            # Print details of the first news item
            if all_news:
                first_item = all_news[0]
                print("\nFirst news item details:")
                print(f"Title: {first_item.get('title', 'N/A')}")
                print(f"Published at: {first_item.get('published_at', 'N/A')}")

                # Safely get preview text
                preview_text = first_item.get('preview_text', 'N/A')
                if preview_text and isinstance(preview_text, str):
                    preview = preview_text[:100] + "..."
                else:
                    preview = "Preview not available"
                print(f"Preview: {preview}")

                # Print all available keys for debugging
                print(f"Available keys: {list(first_item.keys())}")

            return True
        else:
            print(f"WARNING: No news found for {ticker_symbol} using direct Robinhood API")
            return False
    except Exception as e:
        print(f"ERROR: Direct Robinhood news retrieval failed with error: {e}")
        return False

def test_print_catalyst_news(ticker_symbol):
    """Test the print_catalyst_news function for a specific ticker."""
    print(f"\n=== Testing print_catalyst_news for {ticker_symbol} ===")

    # Capture stdout
    captured_output = StringIO()
    with contextlib.redirect_stdout(captured_output):
        print_catalyst_news(ticker_symbol)

    # Get the captured output
    output = captured_output.getvalue()

    # Check if news section exists
    if "--- Catalyst News (Today - Robinhood) ---" in output:
        print("News section header found in output")

        # Check if we got actual news or the "No news found" message
        if "No news published today found from Robinhood" in output:
            print(f"NOTE: No news published today for {ticker_symbol}")
            return True  # This is not a failure, just no news today
        elif "No news found from Robinhood" in output:
            print(f"WARNING: No news found for {ticker_symbol}")
            return False
        elif "Error fetching Robinhood news" in output:
            print(f"ERROR: Error fetching Robinhood news for {ticker_symbol}")
            return False
        else:
            # Check for news items (they should start with "- ")
            news_lines = [line for line in output.split('\n') if line.strip().startswith("- ")]
            if len(news_lines) > 0:
                print(f"SUCCESS: Found {len(news_lines)} news items for {ticker_symbol}")

                # Print the first news item
                if news_lines:
                    print(f"First news item: {news_lines[0]}")

                return True
            else:
                print(f"WARNING: No news items found for {ticker_symbol} in the output")
                return False
    else:
        print("ERROR: News section header not found in output")
        return False

def main():
    """Main function to run the tests."""
    print("=== Robinhood News Functionality Test ===")

    # First, test login
    login_result = test_robinhood_login()
    if not login_result:
        print("\nWARNING: Robinhood login failed. Skipping news tests.")
        return

    # Test a few tickers
    tickers = ["TSLA", "AAPL", "MSFT", "GOOGL", "AMZN"]
    direct_results = []
    wrapper_results = []

    for ticker in tickers:
        print(f"\n=== TESTING {ticker} ===")
        direct_result = test_direct_robinhood_news(ticker)
        wrapper_result = test_print_catalyst_news(ticker)
        direct_results.append(direct_result)
        wrapper_results.append(wrapper_result)

    # Print summary
    print("\n=== TEST SUMMARY ===")
    for i, ticker in enumerate(tickers):
        print(f"{ticker} Direct Robinhood test: {'PASSED' if direct_results[i] else 'FAILED'}")
        print(f"{ticker} print_catalyst_news test: {'PASSED' if wrapper_results[i] else 'FAILED'}")

    # Overall result
    all_direct = all(direct_results)
    all_wrapper = all(wrapper_results)

    print("\n=== OVERALL RESULT ===")
    print(f"Direct Robinhood tests: {'PASSED' if all_direct else 'FAILED'}")
    print(f"print_catalyst_news tests: {'PASSED' if all_wrapper else 'FAILED'}")

    if not all_direct and all_wrapper:
        print("\nNOTE: Direct tests failed but wrapper tests passed. This suggests the wrapper function is handling errors gracefully.")
    elif all_direct and not all_wrapper:
        print("\nWARNING: Direct tests passed but wrapper tests failed. This suggests an issue with the wrapper function.")
    elif not all_direct and not all_wrapper:
        print("\nWARNING: Both direct and wrapper tests failed. This suggests an issue with the Robinhood API or authentication.")

if __name__ == "__main__":
    main()
