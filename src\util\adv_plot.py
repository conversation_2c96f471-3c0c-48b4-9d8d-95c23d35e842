import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from stockdata.data_source import getQuoteTable; np.random.seed(1)


def loadData():
    import os
    # Create data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    os.makedirs(data_dir, exist_ok=True)

    etf_screener_path = os.path.join(data_dir, 'ETF Screener.xlsx')
    etf_output_path = os.path.join(data_dir, 'ETF.xlsx')

    exisiting_df = pd.read_excel(etf_screener_path, skiprows=1)['ETF']
    #print(exisiting_df)
    df = pd.DataFrame(columns=['Ticker', 'Quote', '52WHigh', '52WLow'])
    for etf in exisiting_df.tolist():
        try:
            df = df.append(getQuoteTable(etf), ignore_index=True)
            print("Done", etf)
        except Exception as e:
            print("Error in getting:", etf)
    df.to_excel(etf_output_path, engine='openpyxl')
    print(df)


def load_etf_data():
    import os
    # Create data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    os.makedirs(data_dir, exist_ok=True)

    etf_path = os.path.join(data_dir, 'ETF.xlsx')
    etf_sector_path = os.path.join(data_dir, 'ETF_Sector.xlsx')
    etf_industry_path = os.path.join(data_dir, 'ETF_Industry.xlsx')

    try:
        df = pd.read_excel(etf_path)
        df['GF from Low'] = df['Quote']/df['52WLow']
        df['per from High'] = (df['Quote']-df['52WHigh'])*100/df['52WHigh']
        #plot sector
        sector = pd.read_excel(etf_sector_path, skiprows=1)
        ind = pd.read_excel(etf_industry_path, skiprows=1)
        return df, sector, ind
    except FileNotFoundError:
        print(f"ETF data files not found. Run loadData() first to generate them.")
        return None, None, None

def advPlot(df,sector):


    sectordf = df[df.Ticker.isin(sector.ETF)]
    output = dict()
    for index, row in sector.iterrows():
        output[row['ETF']] = row['Description']
    #print(output)
    sectordf = sectordf.reset_index()

    x = sectordf['GF from Low']
    y = sectordf['per from High']
    #plt.scatter(x, y, c=y, cmap='Greens')


    # for i, txt in enumerate(sectordf['Ticker']):
    #         plt.annotate(txt, (x[i], y[i]), fontsize=8)

    names = np.array(list(output.values()))
    #c = np.random.randint(1,5,size=len(output))

    #norm = plt.Normalize(1,4)
    cmap = plt.cm.Greens


    fig,ax = plt.subplots()
    sc = plt.scatter(x,y, s=15,c=y, cmap=cmap)

    annot = ax.annotate("", xy=(0,0), xytext=(10,10),textcoords="offset points",
                        bbox=dict(boxstyle="round", fc="w"),
                        arrowprops=dict(arrowstyle="->"))

    for i, txt in enumerate(sectordf['Ticker']):
        plt.annotate(txt, (x[i], y[i]), fontsize=7,fontweight=5)

    annot.set_visible(True)

    def update_annot(ind):

        pos = sc.get_offsets()[ind["ind"][0]]
        annot.xy = pos
        text = "{}, {}".format(" ".join(list(map(str,ind["ind"]))),
                            " ".join([names[n] for n in ind["ind"]]))
        annot.set_text(text)
        #annot.get_bbox_patch().set_facecolor(cmap([ind["ind"][0]]))
        #annot.get_bbox_patch().set_alpha(0.4)


    def hover(event):
        vis = annot.get_visible()
        if event.inaxes == ax:
            cont, ind = sc.contains(event)
            if cont:
                update_annot(ind)
                annot.set_visible(True)
                fig.canvas.draw_idle()
            else:
                if vis:
                    annot.set_visible(False)
                    fig.canvas.draw_idle()

    fig.canvas.mpl_connect("motion_notify_event", hover)

    plt.show()

if __name__ == "__main__":
    loadData()
    df, sector, ind = load_etf_data()
    if df is not None:
        advPlot(df, sector)
        advPlot(df, ind)
