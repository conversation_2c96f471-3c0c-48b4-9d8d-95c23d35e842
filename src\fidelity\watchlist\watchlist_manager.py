# watchlist_manager.py - Manages Fidelity watchlists creation and stock addition

import requests
import pandas as pd
from typing import List, Dict
from dotenv import load_dotenv
import os

from fidelity.common.api_headers import get_fidelity_headers # Updated import to new file name

load_dotenv()


class FidelityWatchlistManager:
    def __init__(self):
        """
        Initialize the Fidelity Watchlist Manager

        :param cookie: Authentication cookie from Fidelity
        """
        self.base_url = "https://digital.fidelity.com/ftgw/digital/watwebex/api/graphql?op=lwcSaveWatchlists"
        self.headers = get_fidelity_headers()
        self.created_watchlist_names = set()

    def _generate_unique_watchlist_name(self, base_name: str, suffix: str = '') -> str:
        """
        Generate a unique watchlist name

        :param base_name: Base name for the watchlist
        :param suffix: Optional suffix to distinguish multiple lists
        :return: Unique watchlist name
        """
        # Create a clean base name (remove special characters)
        clean_base_name = ''.join(c for c in base_name if c.isalnum() or c.isspace())

        # Truncate to prevent extremely long names
        clean_base_name = clean_base_name[:30]

        # Add suffix if provided
        full_name = f"{clean_base_name}_{suffix}".strip() if suffix else clean_base_name

        # Ensure uniqueness
        counter = 1
        original_name = full_name
        while full_name in self.created_watchlist_names:
            full_name = f"{original_name}_{counter}"
            counter += 1

        self.created_watchlist_names.add(full_name)
        return full_name

    def create_watchlist(self, name: str) -> str:
        """
        Create a new watchlist

        :param name: Name for the watchlist
        :return: Watchlist ID
        """
        # Generate a unique watchlist name
        watchlist_name = self._generate_unique_watchlist_name(name)

        payload = {
            "query": "mutation lwcSaveWatchlists($watchlistData: LwcSaveWatchlistData) {\n  lwcSaveWatchlists(watchlistData: $watchlistData) {\n    sysMsgs {\n      sysMsg {\n        message\n        detail\n        source\n        code\n        type\n        __typename\n      }\n      __typename\n    }\n    watchListDetails {\n      watchListId\n      securityIds\n      __typename\n    }\n    __typename\n  }\n}",
            "variables": {
                "watchlistData": {
                    "watchListDetails": {
                        "watchListName": watchlist_name,
                        "isDefault": False,
                        "watchListTypeCode": "WL",
                        "securityDetails": []
                    }
                }
            }
        }

        response = requests.post(self.base_url, headers=self.headers, json=payload)
        response_data = response.json()

        # Extract and return the watchlist ID
        return response_data['data']['lwcSaveWatchlists']['watchListDetails'][0]['watchListId']

    def add_stocks_to_watchlist(self, watchlist_id: str, stocks: List[str]) -> None:
        """
        Add stocks to an existing watchlist

        :param watchlist_id: ID of the watchlist to add stocks to
        :param stocks: List of stock symbols to add
        """
        # Limit to 50 stocks max per watchlist
        stocks = stocks[:50]

        # Prepare security details with rank
        security_details = [{"symbol": symbol, "rankId": idx + 1} for idx, symbol in enumerate(stocks)]

        payload = {
            "query": "mutation lwcSaveWatchlists($watchlistData: LwcSaveWatchlistData) {\n  lwcSaveWatchlists(watchlistData: $watchlistData) {\n    sysMsgs {\n      sysMsg {\n        message\n        detail\n        source\n        code\n        type\n        __typename\n      }\n      __typename\n    }\n    watchListDetails {\n      watchListId\n      securityIds\n      __typename\n    }\n    __typename\n  }\n}",
            "variables": {
                "watchlistData": {
                    "watchListDetails": [{
                        "watchListId": watchlist_id,
                        "watchListTypeCode": "WL",
                        "securityDetails": security_details
                    }]
                }
            }
        }

        response = requests.post(self.base_url, headers=self.headers, json=payload)
        response.json()

        # Optional: Add error handling or logging here

# ... existing code ...

def plan_and_create_watchlists(sector_stocks: Dict[str, List[str]], max_stocks_per_watchlist: int = 50) -> List[List[str]]:
    """
    Plans and creates watchlists ensuring that sectors are optimized across watchlists,
    fitting smaller batches into existing partially filled watchlists without exceeding limits.

    :param sector_stocks: Dictionary with sectors as keys and lists of stocks as values
    :param max_stocks_per_watchlist: Maximum number of stocks allowed per watchlist
    :return: List of watchlists containing stock symbols
    """
    watchlists = []  # List to store created watchlists

    # Sort sectors by stock count in descending order
    sorted_sectors = sorted(sector_stocks.items(), key=lambda item: len(item[1]), reverse=True)

    for sector, stocks in sorted_sectors:
        index = 0
        # Process each sector by chunks of maximum size allowable per watchlist
        while index < len(stocks):
            batch = stocks[index:index + max_stocks_per_watchlist]

            # Try to fit the batch into an existing watchlist
            placed_in_existing_watchlist = False
            for i, (_, existing_watchlist) in enumerate(watchlists):
                if len(existing_watchlist) + len(batch) <= max_stocks_per_watchlist:
                    existing_watchlist.extend(batch)
                    existing_sectors, _ = watchlists[i]
                    if sector not in existing_sectors:
                        existing_sectors.append(sector)  # Corrected to use list method
                    placed_in_existing_watchlist = True
                    break

            # If the batch didn't fit, create new watchlist
            if not placed_in_existing_watchlist:
                watchlists.append(([sector], batch))

            # Move to next batch
            index += max_stocks_per_watchlist

    # Print the plan before proceeding
    for i, (sectors, stocks) in enumerate(watchlists, start=1):
        watchlist_name = '_'.join([sector[:3] for sector in sectors])
        print(f"{watchlist_name}:")
        for sector in sectors:
            count = len([s for s in stocks if s in sector_stocks.get(sector, [])])
            print(f"  - {sector}: {count} stocks")
        print(f"  Total stocks: {len(stocks)}\n")

    input("Press Enter to proceed with creating watchlists...")

    return [stocks for _, stocks in watchlists]

def process_stocks_by_sector(csv_path: str) -> None:
    """
    Process stocks from CSV, create sector-contained watchlists.

    :param csv_path: Path to the input CSV file
    :param cookie: Authentication cookie from Fidelity
    """
    df = pd.read_excel(csv_path)
    sector_stocks = df.groupby('Sector')['Symbol'].apply(list).to_dict()

    watchlist_manager = FidelityWatchlistManager()

    sector_based_watchlists = plan_and_create_watchlists(sector_stocks)

    for idx, stocks in enumerate(sector_based_watchlists):
        sectors_in_watchlist = {sector[:3] for sector in sector_stocks if any(stock in stocks for stock in sector_stocks[sector])}
        watchlist_name = "_".join(sectors_in_watchlist)
        watchlist_id = watchlist_manager.create_watchlist(watchlist_name)
        print(f"Created {watchlist_name} with ID: {watchlist_id}")

        watchlist_manager.add_stocks_to_watchlist(watchlist_id, stocks)
        print(f"Added stocks to {watchlist_name}: {len(stocks)} stocks")

# ... existing code ...
def main():
    # Replace with your actual cookie and CSV path
    CSV_PATH = os.getenv('FIDELITY_FILE')

    # Choose the processing method
    process_stocks_by_sector(CSV_PATH)  # Maximized sector-based approach

if __name__ == "__main__":
    main()
