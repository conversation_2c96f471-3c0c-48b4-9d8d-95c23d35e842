#!/usr/bin/env python
"""
Cleanup script to remove sensitive information from the repository
before making it public.
"""

import os
import shutil
import glob

def main():
    """Main cleanup function"""
    print("Starting cleanup process...")
    
    # Files to remove
    sensitive_files = [
        "*.log",
        "*.pickle",
        "token.*",
        "*.db",
        "*.sqlite",
        "credentials.json",
        # Add any other patterns here
    ]
    
    # Directories to remove
    sensitive_dirs = [
        "cache",
        "__pycache__",
        "tmp",
        # Add any other directory patterns here
    ]
    
    # Count of removed items
    removed_files = 0
    removed_dirs = 0
    
    # Remove sensitive files
    for pattern in sensitive_files:
        for file_path in glob.glob(f"**/{pattern}", recursive=True):
            if os.path.isfile(file_path):
                try:
                    os.remove(file_path)
                    print(f"Removed file: {file_path}")
                    removed_files += 1
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")
    
    # Remove sensitive directories
    for dir_pattern in sensitive_dirs:
        for dir_path in glob.glob(f"**/{dir_pattern}", recursive=True):
            if os.path.isdir(dir_path):
                try:
                    shutil.rmtree(dir_path)
                    print(f"Removed directory: {dir_path}")
                    removed_dirs += 1
                except Exception as e:
                    print(f"Error removing {dir_path}: {e}")
    
    print(f"\nCleanup complete! Removed {removed_files} files and {removed_dirs} directories.")
    print("\nNOTE: You should now update .gitignore to ensure these files don't get committed in the future.")
    print("      Then commit the changes with: git add -A && git commit -m 'Remove sensitive files and data'")

if __name__ == "__main__":
    main()
