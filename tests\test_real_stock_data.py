"""
Test script to verify that the optimized calculations work with real stock data.
"""

import sys
import os
import pandas as pd
import numpy as np
import time

# Add src directory to Python path
sys.path.append('src')

from stockdata.data_source import getStockDataV3

def test_real_stock_data():
    """Test the optimized calculations with real stock data."""
    print("Testing optimized calculations with real stock data...")
    
    # Test with a few different stocks
    tickers = ['AAPL', 'MSFT', 'NVDA']
    
    for ticker in tickers:
        print(f"\nTesting {ticker}...")
        
        # Get stock data
        start_time = time.time()
        df = getStockDataV3(ticker)
        load_time = time.time() - start_time
        
        if df is not None and not df.empty:
            print(f"Success! Retrieved data for {ticker} in {load_time:.2f} seconds")
            print(f"Shape: {df.shape}")
            print(f"Date range: {df.index.min()} to {df.index.max()}")
            
            # Check if the TTM column exists and has non-placeholder values
            if 'TTM' in df.columns:
                ttm_values = df['TTM'].value_counts()
                print("\nTTM values distribution:")
                print(ttm_values)
                
                if len(ttm_values) > 1:  # If there are both True and False values
                    print("✅ TTM calculation is working correctly!")
                else:
                    print("❌ TTM calculation might be using placeholder values only.")
            else:
                print("❌ TTM column not found in the DataFrame.")
            
            # Check if the $VolumeM column exists and has meaningful values
            if '$VolumeM' in df.columns:
                volume_stats = df['$VolumeM'].describe()
                print("\n$VolumeM statistics:")
                print(volume_stats)
                
                if volume_stats['std'] > 0:  # If there's variation in the values
                    print("✅ Volume calculation is working correctly!")
                else:
                    print("❌ Volume calculation might be using placeholder values only.")
            else:
                print("❌ $VolumeM column not found in the DataFrame.")
            
            # Check extension values
            if 'ext_20' in df.columns:
                ext_stats = df['ext_20'].describe()
                print("\next_20 statistics:")
                print(ext_stats)
                
                if ext_stats['std'] > 0:  # If there's variation in the values
                    print("✅ Extension calculation is working correctly!")
                else:
                    print("❌ Extension calculation might be using placeholder values only.")
            else:
                print("❌ ext_20 column not found in the DataFrame.")
            
            # Print the first few rows to see the actual values
            print("\nFirst 5 rows with TTM, $VolumeM, and ext_20 columns:")
            print(df[['TTM', '$VolumeM', 'ext_20']].head())
        else:
            print(f"Failed to retrieve data for {ticker}")

if __name__ == "__main__":
    test_real_stock_data()
