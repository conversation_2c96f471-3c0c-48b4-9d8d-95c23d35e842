import numpy as np
import pandas as pd



def calcATR(data,period):
    high_low = data['High'] - data['Low']
    high_close = np.abs(data['High'] - data['Close'].shift(-1))
    low_close = np.abs(data['Low'] - data['Close'].shift(-1))
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = np.max(ranges, axis=1)
    return true_range.rolling(period).sum()/14

def calcATRV2(data,period):
    high_low = data['High'] - data['Low']
    high_close = np.abs(data['High'] - data['Close'].shift(-1))
    low_close = np.abs(data['Low'] - data['Close'].shift(-1))
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = np.max(ranges, axis=1)
    return true_range.rolling(period).sum()/14    

