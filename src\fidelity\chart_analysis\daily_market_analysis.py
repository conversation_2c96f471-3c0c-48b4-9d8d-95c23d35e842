# daily_market_analysis.py - Performs daily analysis on market data and stock charts
#
# This module provides functions for analyzing daily stock market data and generating
# charts for selected stocks. It's designed to be run on a daily basis to identify
# trading opportunities based on the day's market action.
#
# Usage:
#   1. Run the chartAnalysis() function directly: python -m fidelity.chart_analysis.daily_market_analysis
#   2. The module will load data from 'screener_results.xls', process it, and download charts
#
# Required DataFrame columns:
#   - Symbol: Stock ticker symbol
#   - % Price off 10 Day SMA: Percentage deviation from 10-day simple moving average
#   - % Price off 20 Day SMA: Percentage deviation from 20-day simple moving average
#   - % Price off 50 Day SMA: Percentage deviation from 50-day simple moving average
#   - Sector: Stock's sector classification
#   - Industry: Stock's industry classification
#   - Sub-Industry: Stock's sub-industry classification
#   - Security Type: Type of security (e.g., 'Common Stock', 'Common Stock (REIT)')

from util.scrapper import downloadChartsFidelity # Corrected absolute import from root
import pandas as pd

def convergence(screener):
    """
    Calculate price convergence metrics for stocks based on their moving averages.

    This function identifies stocks where the price is converging toward multiple moving averages,
    which can signal potential reversal or continuation patterns. Lower convergence values indicate
    tighter alignment between moving averages.

    Args:
        screener (pd.DataFrame): DataFrame containing stock data with required SMA columns

    Returns:
        pd.DataFrame: The processed DataFrame with added 'Convergence' column

    Note:
        Also saves the sorted results to 'sorted_screener_convg.xlsx'
    """
    # Remove rows with missing 20-day SMA data
    screener = screener.dropna(subset=['% Price off 20 Day SMA'])

    # Sort by 20-day SMA deviation
    screener = screener.sort_values(by='% Price off 20 Day SMA')

    # Calculate convergence as the sum of absolute differences between SMA deviations
    # Lower values indicate tighter convergence between moving averages
    screener['Convergence'] = screener[['% Price off 50 Day SMA', '% Price off 20 Day SMA','% Price off 10 Day SMA']].diff().abs().sum(axis=1)

    # Sort by convergence value (ascending = tighter convergence first)
    df_sorted = screener.sort_values(by='Convergence', ascending=True)

    # Save results to Excel
    df_sorted.to_excel("sorted_screener_convg.xlsx")

    return screener

def groupAndFilter(df):
    """
    Group and filter stocks by sector for daily analysis.

    This function organizes stocks by sector, removing REITs and sorting by sector size.
    It's designed for daily analysis to identify the most active sectors and stocks.

    Args:
        df (pd.DataFrame): DataFrame containing stock data with required classification columns

    Returns:
        pd.DataFrame: Processed and sorted DataFrame

    Note:
        Also saves the sorted results to 'sorted_screener.xlsx'
    """
    # Remove REITs from the analysis
    df = df[df['Security Type'] != 'Common Stock (REIT)']

    # Count stocks in each sector to identify dominant sectors
    sector_counts = df['Sector'].value_counts().reset_index(name='Sector_Count')
    sector_counts.columns = ['Sector', 'Sector_Count']

    # Add sector counts to the main dataframe
    df = pd.merge(df, sector_counts, on='Sector')

    # Sort by sector size (largest sectors first), then by industry and sub-industry
    df = df.sort_values(
        by=['Sector_Count', 'Industry', 'Sub-Industry'],
        ascending=[False, False, False]
    )

    # Remove the temporary counting column
    df = df.drop('Sector_Count', axis=1)

    # Ensure no duplicate symbols
    df = df.drop_duplicates(subset='Symbol')

    # Save results to Excel for reference
    df.to_excel("sorted_screener.xlsx")

    return df



def chartAnalysis():
    """
    Perform daily chart analysis on stocks from a screener results file.

    This function loads stock data from an Excel file, extracts ticker symbols,
    and downloads charts for those symbols using the Fidelity chart downloader.

    The charts are saved to the 'fidelity_daily' directory for review and analysis.
    """
    # Load stock data from Excel file
    import os

    # Create data directory if it doesn't exist
    screener_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'data', 'screener')
    os.makedirs(screener_dir, exist_ok=True)

    # Path to screener results file
    screener_file = os.path.join(screener_dir, "screener_results.xls")

    df = pd.read_excel(screener_file)

    # Use the dataframe directly without additional filtering
    # Uncomment the following lines to apply additional filtering if needed
    # screener = groupAndFilter(df)
    # df_sorted = convergence(screener)
    df_sorted = df

    # Extract ticker symbols from the dataframe
    tickers = df_sorted["Symbol"].to_list()

    # Filter to valid ticker symbols (strings with 5 or fewer characters)
    stock_symbols = [entry for entry in tickers if isinstance(entry, str) and len(entry) <= 5]

    # Print the filtered symbols and count
    print(stock_symbols)
    print(len(stock_symbols))

    # Download charts for the filtered symbols
    downloadChartsFidelity(stock_symbols, "fidelity_daily")


# Execute the chart analysis when this module is run directly
if __name__ == "__main__":
    chartAnalysis()
