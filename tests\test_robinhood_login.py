"""
Test script to debug Robinhood login and news functionality.
"""

import sys
import os
import traceback

# Add src directory to Python path
sys.path.append('src')

def test_robinhood_login():
    """Test Robinhood login functionality."""
    try:
        from ticker_analyzer.data_fetcher import login_robinhood
        import robin_stocks.robinhood as r

        print("\n=== Testing Robinhood Login ===")

        # Check if environment variables are set
        rh_un = os.environ.get('RH_UN')
        rh_pw = os.environ.get('RH_PW')

        if not rh_un or not rh_pw:
            print("WARNING: Robinhood credentials not found in environment variables.")
            return False

        # Try to login
        try:
            login_result = login_robinhood()
            if login_result:
                print("SUCCESS: Robinhood login successful")

                # Test if we can get some basic data
                try:
                    profile = r.load_account_profile()
                    print(f"Account profile loaded: {profile.get('first_name')} {profile.get('last_name')}")
                    return True
                except Exception as e:
                    print(f"ERROR: Could not load account profile: {e}")
                    traceback.print_exc()
                    return False
            else:
                print("WARNING: Robinhood login failed")
                return False
        except Exception as e:
            print(f"ERROR: Robinhood login failed with exception: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_robinhood_news(symbol="TSLA"):
    """Test Robinhood news functionality."""
    try:
        import robin_stocks.robinhood as r
        from datetime import datetime, timezone

        print(f"\n=== Testing Robinhood News for {symbol} ===")

        # Try to get news
        try:
            all_news = r.stocks.get_news(symbol)

            if all_news and isinstance(all_news, list):
                print(f"SUCCESS: Found {len(all_news)} news items for {symbol}")

                # Print the first news item
                if len(all_news) > 0:
                    first_item = all_news[0]
                    print("\nFirst news item:")
                    print(f"Title: {first_item.get('title', 'N/A')}")
                    print(f"Published at: {first_item.get('published_at', 'N/A')}")
                    preview_text = first_item.get('preview_text', 'N/A')
                    if preview_text and isinstance(preview_text, str):
                        preview = f"{preview_text[:100]}..."
                    else:
                        preview = "Preview not available"
                    print(f"Preview: {preview}")
                    print(f"Available keys: {list(first_item.keys())}")

                # Check for today's news
                today = datetime.now(timezone.utc).date().isoformat()
                todays_news = [news for news in all_news if news.get('published_at', '').startswith(today)]

                if todays_news:
                    print(f"\nFound {len(todays_news)} news items published today ({today})")
                    for news in todays_news[:2]:
                        print(f"- {news.get('title', 'N/A')}")
                        print(f"  Published: {news.get('published_at', 'N/A')}")
                else:
                    print(f"\nNo news published today ({today}) found for {symbol}")

                return True
            else:
                print(f"WARNING: No news found for {symbol}")
                return False
        except Exception as e:
            print(f"ERROR: Could not get news: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def main():
    """Main function to run the tests."""
    print("=== Testing Robinhood Functionality ===")

    # Test login
    login_result = test_robinhood_login()

    if login_result:
        # Test news for different symbols
        symbols = ["TSLA", "AAPL", "MSFT", "GOOGL", "AMZN"]
        for symbol in symbols:
            test_robinhood_news(symbol)
    else:
        print("\nSkipping news tests because login failed.")

if __name__ == "__main__":
    main()
