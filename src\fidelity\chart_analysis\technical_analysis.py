# technical_analysis.py - Performs technical analysis on stock charts, focusing on price convergence
#
# This module provides functions for analyzing stock price data with a focus on technical indicators
# and price convergence patterns. It's primarily used for identifying stocks where moving averages
# are converging, which can signal potential trading opportunities.
#
# Usage:
#   1. Import the module: from fidelity.chart_analysis.technical_analysis import convergence, groupAndFilter
#   2. Load your stock data into a pandas DataFrame with required columns (Symbol, % Price off X Day SMA)
#   3. Use the convergence() function to calculate convergence metrics
#   4. Use groupAndFilter() to organize stocks by sector, industry, and sub-industry
#
# Required DataFrame columns:
#   - Symbol: Stock ticker symbol
#   - % Price off 10 Day SMA: Percentage deviation from 10-day simple moving average
#   - % Price off 20 Day SMA: Percentage deviation from 20-day simple moving average
#   - % Price off 50 Day SMA: Percentage deviation from 50-day simple moving average
#   - Sector: Stock's sector classification
#   - Industry: Stock's industry classification
#   - Sub-Industry: Stock's sub-industry classification
#   - Security Type: Type of security (e.g., 'Common Stock', 'Common Stock (REIT)')

import pandas as pd
import asyncio
from util.util import read_and_filter_csv # Corrected absolute import from root
import glob
from fidelity.chart_analysis.chart_downloader import downloadChartsFidelityV2 # Updated import to new file name

def convergence(screener):
    """
    Calculate price convergence metrics for stocks based on their moving averages.

    This function identifies stocks where the price is converging toward multiple moving averages,
    which can signal potential reversal or continuation patterns. Lower convergence values indicate
    tighter alignment between moving averages.

    Args:
        screener (pd.DataFrame): DataFrame containing stock data with required SMA columns

    Returns:
        pd.DataFrame: The processed DataFrame with added 'Convergence' column

    Note:
        Also saves the sorted results to 'sorted_screener_convg.xlsx'
    """
    # Remove rows with missing 20-day SMA data
    screener = screener.dropna(subset=['% Price off 20 Day SMA'])

    # Sort by 20-day SMA deviation
    screener = screener.sort_values(by='% Price off 20 Day SMA')

    # Calculate convergence as the sum of absolute differences between SMA deviations
    # Lower values indicate tighter convergence between moving averages
    screener['Convergence'] = screener[['% Price off 50 Day SMA', '% Price off 20 Day SMA','% Price off 10 Day SMA']].diff().abs().sum(axis=1)

    # Sort by convergence value (ascending = tighter convergence first)
    df_sorted = screener.sort_values(by='Convergence', ascending=True)

    # Save results to Excel
    df_sorted.to_excel("sorted_screener_convg.xlsx")

    return screener

def printFilteredData(tickers):
    """
    Filter and print stock symbols from a list, keeping only valid ticker symbols.

    This function filters a list to keep only string entries with 5 or fewer characters,
    which is the standard format for most stock ticker symbols.

    Args:
        tickers (list): List of potential stock symbols to filter

    Returns:
        None: Results are printed to console
    """
    # Filter to keep only string entries with 5 or fewer characters (valid ticker format)
    stock_symbols = [entry for entry in tickers if isinstance(entry, str) and len(entry) <= 5]

    # Print the filtered list of symbols
    print(stock_symbols)

    # Print the count of symbols
    print(len(stock_symbols))

def groupAndFilter(df, filter=None):
    """
    Group and filter stocks by sector, industry, and sub-industry with ranking.

    This function organizes stocks into hierarchical groups by sector, industry, and sub-industry,
    then ranks them within these groups based on their percentile values. It's useful for identifying
    the strongest stocks within the strongest sectors and industries.

    Args:
        df (pd.DataFrame): DataFrame containing stock data with required classification columns
        filter (str, optional): Optional filter parameter (not used in this implementation)

    Returns:
        pd.DataFrame: Processed and sorted DataFrame with additional grouping and ranking columns
    """
    # Remove REITs from the analysis
    df = df[df['Security Type'] != 'Common Stock (REIT)']

    # Count stocks in each sector to identify dominant sectors
    sector_counts = df['Sector'].value_counts().reset_index(name='Sector_Count')
    sector_counts.columns = ['Sector', 'Sector_Count']

    # Add sector counts to the main dataframe
    df = pd.merge(df, sector_counts, on='Sector')

    # Calculate group sizes at each classification level
    df['Sector_Count'] = df.groupby('Sector')['Percentile'].transform('size')
    df['Industry_Count'] = df.groupby(['Sector', 'Industry'])['Percentile'].transform('size')
    df['SubIndustry_Count'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].transform('size')

    # Sort by group size (largest groups first)
    df = df.sort_values(
        by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'],
        ascending=[False, False, False]
    )

    # Rank stocks within each sub-industry group based on percentile
    df['Rank'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].rank(ascending=False)

    # Final sort: prioritize largest groups, then best-ranked stocks within each group
    df = df.sort_values(
        by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count', 'Rank'],
        ascending=[False, False, False, True]
    )

    return df



def chartAnalysis():
    """
    Perform chart analysis on stocks from screener results files.

    This function:
    1. Loads stock data from all Excel files matching 'screener_results*.xls'
    2. Combines the data and removes duplicates
    3. Calculates dollar volume (price * volume)
    4. Filters stocks based on dollar volume
    5. Downloads charts for the filtered stocks
    """
    # Use glob to find all files that start with "screener_results"
    file_list = glob.glob("screener_results*.xls")

    if not file_list:
        print("No files found with the pattern 'screener_results*.xls'")
        return

    df = pd.DataFrame()

    # Concatenate all files matching the pattern
    for file in file_list:
        df_temp = pd.read_excel(file)
        df = pd.concat([df, df_temp], ignore_index=True)

    # Drop duplicate rows based on the 'Symbol' column
    df = df.drop_duplicates(subset='Symbol')

    # Calculate dollar volume (price * average daily volume)
    # This is an important liquidity metric
    df['$Volume'] = df["Volume (10 Day Avg)"] * df["Security Price"]

    # Filter out rows where $Volume is less than 25
    df = df[df['$Volume'] >= 25]

    #print(df[['Symbol', '$Volume']].head(25))


    tickers = df["Symbol"].to_list()

    printFilteredData(tickers)

    # Read and filter CSV data for additional filtering
    # ... existing code ...

    rs_filtered_df = read_and_filter_csv(int(input("Enter RS score:")))

    df = df[df['Symbol'].isin(rs_filtered_df['Ticker'])]


    # Merge the additional columns into the screener dataframe
    df = df.merge(rs_filtered_df[['Ticker', 'Percentile']], left_on='Symbol', right_on='Ticker', how='left')


    filter = input("Earnings Filter needed(Y/N):")

    # Filter the screener DataFrame with CSV data
    screener = groupAndFilter(df, filter)

    printFilteredData( tickers = screener["Symbol"].to_list())



    # Drop the extra Ticker column if needed, since 'Symbol' and 'Ticker' are used for merging
    screener.drop(columns=['Ticker'], inplace=True)

    # done = ["TSM","APP"]
    # screener = screener[screener['Symbol'].isin(done)]

    #df_sorted = convergence(screener)

    df_sorted = screener

    tickers = df_sorted["Symbol"].to_list()

    printFilteredData(tickers)


    print("Start? (Y/N):")

    start = input()


    if start.strip().upper() == 'Y':
       df_sorted.to_excel("sorted_screener.xlsx")
       save_file = input('Save File True or False: ').strip().lower() == 'true'
       if save_file:
           asyncio.run(downloadChartsFidelityV2(df_sorted, "fidelity_screen"))
       else:
           asyncio.run(downloadChartsFidelityV2(df_sorted))

chartAnalysis()
