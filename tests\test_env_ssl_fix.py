"""
Test script to verify that the environment variable-based SSL certificate verification fix works.
This script tests downloading data for tickers using the environment variables set in the batch file.
"""

import sys
import os
import requests

# Add src directory to Python path
sys.path.append('src')

import yfinance as yf

def test_env_variables():
    """Test if the environment variables are set correctly."""
    print("\n=== Testing Environment Variables ===")
    
    ssl_cert_file = os.environ.get('SSL_CERT_FILE')
    requests_ca_bundle = os.environ.get('REQUESTS_CA_BUNDLE')
    curl_ca_bundle = os.environ.get('CURL_CA_BUNDLE')
    
    print(f"SSL_CERT_FILE: {ssl_cert_file}")
    print(f"REQUESTS_CA_BUNDLE: {requests_ca_bundle}")
    print(f"CURL_CA_BUNDLE: {curl_ca_bundle}")
    
    # Check if the certificate files exist
    if ssl_cert_file and os.path.exists(ssl_cert_file):
        print(f"SSL_CERT_FILE exists: {ssl_cert_file}")
    elif ssl_cert_file:
        print(f"SSL_CERT_FILE does not exist: {ssl_cert_file}")
    
    if requests_ca_bundle and os.path.exists(requests_ca_bundle):
        print(f"REQUESTS_CA_BUNDLE exists: {requests_ca_bundle}")
    elif requests_ca_bundle:
        print(f"REQUESTS_CA_BUNDLE does not exist: {requests_ca_bundle}")
    
    if curl_ca_bundle and os.path.exists(curl_ca_bundle):
        print(f"CURL_CA_BUNDLE exists: {curl_ca_bundle}")
    elif curl_ca_bundle:
        print(f"CURL_CA_BUNDLE does not exist: {curl_ca_bundle}")
    
    return bool(ssl_cert_file) and bool(requests_ca_bundle) and bool(curl_ca_bundle)

def test_requests_with_env_ssl():
    """Test making a request with the environment-based SSL verification."""
    print("\n=== Testing requests with environment SSL settings ===")
    
    try:
        response = requests.get('https://api.github.com')
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.json().get('current_user_url', 'No current_user_url found')}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error making request: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_yfinance_with_env_ssl():
    """Test downloading data with environment-based SSL verification."""
    print("\n=== Testing yfinance with environment SSL settings ===")
    
    ticker = 'AAPL'
    try:
        print(f"Attempting to download data for {ticker}...")
        df = yf.download(ticker, period='1wk', progress=True)
        
        if df is not None and not df.empty:
            print(f"Success! Downloaded {len(df)} rows of data for {ticker}")
            print("\nFirst 3 rows:")
            print(df.head(3))
            return True
        else:
            print(f"Error: No data downloaded for {ticker}")
            return False
    except Exception as e:
        print(f"Error downloading {ticker}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all SSL environment variable tests."""
    results = {}
    
    # Test environment variables
    results['env_variables'] = test_env_variables()
    
    # Test requests
    results['requests'] = test_requests_with_env_ssl()
    
    # Test yfinance
    results['yfinance'] = test_yfinance_with_env_ssl()
    
    # Print summary
    print("\n=== TEST SUMMARY ===")
    for test_name, success in results.items():
        print(f"{test_name}: {'SUCCESS' if success else 'FAILURE'}")
    
    # Return True if all tests passed
    return all(results.values())

if __name__ == "__main__":
    print("Starting environment-based SSL fix tests...")
    result = run_all_tests()
    print(f"\nOverall test result: {'SUCCESS' if result else 'FAILURE'}")
