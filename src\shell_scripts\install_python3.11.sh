#!/bin/bash

# Script to install Python 3.11 on Raspbian
# This is useful if you need a newer Python version than what comes with Raspbian

echo "Installing Python 3.11 on Raspbian..."
echo "This script will install Python 3.11 from source, which may take a long time on a Raspberry Pi."
echo "Make sure you have at least 2GB of free disk space and a good power supply."
echo ""
read -p "Continue? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Installation cancelled."
    exit 0
fi

# Install dependencies
echo "Installing dependencies..."
sudo apt-get update
sudo apt-get install -y build-essential libssl-dev zlib1g-dev \
libncurses5-dev libncursesw5-dev libreadline-dev libsqlite3-dev \
libgdbm-dev libdb5.3-dev libbz2-dev libexpat1-dev liblzma-dev \
libffi-dev tk-dev libgdbm-compat-dev

# Create a temporary directory
mkdir -p ~/python_install
cd ~/python_install

# Download Python 3.11
echo "Downloading Python 3.11.8..."
wget https://www.python.org/ftp/python/3.11.8/Python-3.11.8.tgz

# Extract the archive
echo "Extracting Python 3.11.8..."
tar -xf Python-3.11.8.tgz
cd Python-3.11.8

# Configure and build Python
echo "Configuring Python 3.11.8..."
./configure --enable-optimizations

echo "Building Python 3.11.8 (this will take a long time)..."
echo "Consider using 'make -j 4' for a quad-core Raspberry Pi to speed up the build."
read -p "How many cores do you want to use for compilation? (1-4, default: 1): " cores
cores=${cores:-1}

make -j $cores

# Install Python
echo "Installing Python 3.11.8..."
sudo make altinstall

# Clean up
echo "Cleaning up..."
cd ~
rm -rf ~/python_install

# Verify installation
echo "Verifying installation..."
python3.11 --version

echo ""
echo "Python 3.11.8 has been installed as 'python3.11'."
echo "You can now use it to create a virtual environment:"
echo "python3.11 -m venv venv"
echo ""
echo "To make it the default python3, you can create a symbolic link:"
echo "sudo ln -sf /usr/local/bin/python3.11 /usr/local/bin/python3"
echo "But be careful as this might break system tools that expect a specific Python version."
