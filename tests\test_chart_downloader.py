"""
Test script for the download_image function in chart_downloader.py.

This module contains tests for the download_image function using real HTTP requests
to test the function's behavior with actual network connections.
"""

import os
import sys
import tempfile
import time
import traceback

# Add src directory to Python path
sys.path.append('src')

# Import the function to test
from fidelity.chart_analysis.chart_downloader import download_image

def test_download_image():
    """Test the download_image function with real HTTP requests."""
    print("Testing download_image function...")

    # Create a temporary directory for test files
    temp_dir = tempfile.mkdtemp()
    test_file_path = os.path.join(temp_dir, "test_image.png")

    # Create data directory if it doesn't exist
    os.makedirs('src/data/images', exist_ok=True)

    try:
        # Test 1: Download image with specific URL that previously caused "stream mode is not enabled" error
        print("\nTest 1: Downloading TSLA chart image with specific URL...")
        url = 'https://stockcharts.com/c-sc/sc?s=TSLA&p=D&yr=1&mn=0&dy=0&i=t7319146369c&r=1748042323551'

        result = download_image(url, test_file_path)

        if result and os.path.exists(test_file_path):
            file_size = os.path.getsize(test_file_path)
            print(f"Success! Downloaded image from URL")
            print(f"Image size: {file_size} bytes")

            if file_size > 0:
                print("Image file contains data")
            else:
                print("Warning: Image file is empty (0 bytes)")
                return False
        else:
            print(f"Failed to download image from URL")
            # If this URL is no longer valid, don't fail the test
            print("This URL might have expired, continuing with other tests...")

        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

        # Test 2: Download image with a generic URL format
        print("\nTest 2: Downloading AAPL chart image with generic URL format...")
        ticker = "AAPL"
        url = f'https://stockcharts.com/c-sc/sc?s={ticker}&p=D&yr=0&mn=6&dy=0&i=p&r=1681'

        result = download_image(url, test_file_path)

        if result and os.path.exists(test_file_path):
            file_size = os.path.getsize(test_file_path)
            print(f"Success! Downloaded image for {ticker}")
            print(f"Image size: {file_size} bytes")

            if file_size > 0:
                print("Image file contains data")
            else:
                print("Warning: Image file is empty (0 bytes)")
                return False
        else:
            print(f"Failed to download image for {ticker}")
            return False

        return True

    except Exception as e:
        print(f"Error occurred during testing: {e}")
        traceback.print_exc()
        return False
    finally:
        # Clean up the temporary directory and files
        if os.path.exists(test_file_path):
            try:
                os.remove(test_file_path)
                print("Test file removed successfully")
            except Exception as e:
                print(f"Error removing test file: {e}")

        try:
            os.rmdir(temp_dir)
            print("Temporary directory removed successfully")
        except Exception as e:
            print(f"Error removing temporary directory: {e}")


def run_tests():
    """Run the download_image tests."""
    return test_download_image()


if __name__ == '__main__':
    print("Starting chart_downloader tests...")
    print("Python version:", sys.version)
    print("Current working directory:", os.getcwd())
    print("sys.path:", sys.path)
    try:
        success = run_tests()
        print("Tests completed with result:", "SUCCESS" if success else "FAILURE")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Error running tests: {e}")
        traceback.print_exc()
        sys.exit(1)
