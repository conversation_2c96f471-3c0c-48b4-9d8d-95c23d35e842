import requests
from dotenv import load_dotenv

load_dotenv()
from fidelity.common.headers import get_fidelity_headers # Corrected absolute import

def get_watchlists():
    headers = get_fidelity_headers()
    query_data = {
        "operationName": "LwcGetWatchLists",
        "variables": {
            "watchlistData": {
                "watchlists": [{
                    "watchListIds": [],
                    "watchListTypeCode": "WL"
                }],
                "includeWatchListSecurityDetails": True
            }
        },
        "query": "query LwcGetWatchLists($watchlistData: GetWatchlistsReq) { lwcGetWatchLists(watchlistData: $watchlistData) { sysMsgs { sysMsg { message detail source code type __typename } __typename } watchListDetails { watchListName watchListTypeCode watchListId productCode cashBalance isDefault lastUpdatedTime securityDetails { securityId cusip symbol rankId shareQuantity watchCloselyInd priceDetail { purchasePrice purchaseDate __typename } note __typename } __typename } __typename } }"
    }

    response = requests.post('https://digital.fidelity.com/ftgw/digital/watwebex/api/graphql?op=LwcGetWatchLists',
                             headers=headers, json=query_data)

    if response.status_code == 200:
        return response.json()['data']['lwcGetWatchLists']['watchListDetails']
    else:
        print(f"Failed to retrieve watchlists: {response.status_code}")
        return None

def delete_watchlist(watchlist_id):
    headers = get_fidelity_headers()

    mutation_data = {
        "operationName": "LwcDeleteWatchlist",
        "variables": {
            "deleteWatchListInput": {
                "watchListDeleteDetails": [{
                    "watchListId": watchlist_id,
                    "watchListTypeCode": "WL"
                }]
            }
        },
        "query": "mutation LwcDeleteWatchlist($deleteWatchListInput: DeleteWatchListInput) { lwcDeleteWatchlist(deleteWatchListInput: $deleteWatchListInput) { sysMsgs { sysMsg { type code message source detail __typename } __typename } __typename } }"
    }

    response = requests.post('https://digital.fidelity.com/ftgw/digital/watwebex/api/graphql?op=LwcDeleteWatchlist',
                             headers=headers, json=mutation_data)

    if response.status_code == 200:
        print("Watchlist deleted successfully.")
    else:
        print(f"Failed to delete watchlist: {response.status_code}")

def main():

    watchlists = get_watchlists()

    if not watchlists:
        return
    for index, wl in enumerate(watchlists):
        print(f"{index}: {wl['watchListName']} (ID: {wl['watchListId']})")
    try:
     choices = input("Enter the numbers of the watchlists to delete (comma-separated): ")
     choice_list = [int(choice) for choice in choices.split(',') if choice.strip().isdigit()]

     for choice in choice_list:
         if 0 <= choice < len(watchlists):
             delete_watchlist(watchlists[choice]['watchListId'])
         else:
             print(f"Invalid choice: {choice}")
    except ValueError:
      print("Please enter valid numbers.")

if __name__ == "__main__":
    main()
