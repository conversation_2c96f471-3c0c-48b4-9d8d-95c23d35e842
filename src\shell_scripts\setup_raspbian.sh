#!/bin/bash

# Setup script for PythonicFin on Raspbian Linux
# This script sets up the Python virtual environment and installs dependencies

# Change to the project root directory
cd "$(dirname "$0")/../.."
PROJECT_ROOT=$(pwd)

echo "Setting up PythonicFin environment on Raspbian..."

# Check if Python is installed and find the best version to use
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check for Python 3.11 first (preferred version)
if command -v python3.11 &> /dev/null; then
    PYTHON_CMD="python3.11"
    echo "Found Python 3.11 - using this version"
# Check for Python 3.9 next
elif command -v python3.9 &> /dev/null; then
    PYTHON_CMD="python3.9"
    echo "Found Python 3.9 - using this version"
# Fall back to default python3
else
    PYTHON_CMD="python3"
    # Check Python version
    PYTHON_VERSION=$($PYTHON_CMD --version | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

    echo "Using default Python version: $PYTHON_VERSION"

    # Warn if Python version is too old
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 7 ]); then
        echo "Warning: Python version $PYTHON_VERSION is too old. Python 3.7+ is recommended."
        echo "Some features may not work correctly."
        echo "Consider running ./src/shell_scripts/install_python3.11.sh to install Python 3.11"
        read -p "Continue anyway? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Exiting. Please install a newer version of Python."
            exit 1
        fi
    fi
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment using $PYTHON_CMD..."
    $PYTHON_CMD -m venv venv
    if [ $? -ne 0 ]; then
        echo "Failed to create virtual environment."
        echo "You may need to install the venv module:"
        echo "sudo apt-get install python3-venv"
        exit 1
    fi
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install required packages
echo "Installing required packages..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
else
    echo "Warning: requirements.txt not found. Creating a basic one..."

    # Create a basic requirements.txt file with essential packages
    cat > requirements.txt << EOF
pandas
numpy
matplotlib
yfinance==0.1.47
requests
beautifulsoup4
python-dotenv
docx
pillow
openpyxl
EOF

    pip install -r requirements.txt
fi

# Create data directories if they don't exist
echo "Creating data directories..."
mkdir -p src/data/images
mkdir -p src/data/screener

# Make shell scripts executable
echo "Making shell scripts executable..."
chmod +x src/shell_scripts/*.sh

echo ""
echo "Setup complete! You can now run the PythonicFin tools using the shell scripts."
echo ""
echo "Example commands:"
echo "  ./src/shell_scripts/run_tool.sh ticker AAPL"
echo "  ./src/shell_scripts/run_tool.sh screener"
echo "  ./src/shell_scripts/run_tool.sh enhanced"
echo ""
echo "Note: Make sure you have a .env file with necessary environment variables."
