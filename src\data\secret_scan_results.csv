"File","Pattern","LineNumbers"
"C:\Users\<USER>\Desktop\pythonicfin\.env","api_key","3, 10, 13, 16, 18"
"C:\Users\<USER>\Desktop\pythonicfin\.env","secret_key","17"
"C:\Users\<USER>\Desktop\pythonicfin\.env","credential","5, 12"
"C:\Users\<USER>\Desktop\pythonicfin\.env","OvDtfFpZaBhvf4mgKV9g7MSNrcxceFK4B0bfjStH","17"
"C:\Users\<USER>\Desktop\pythonicfin\.env.sample","api_key","11, 14, 17, 24, 28"
"C:\Users\<USER>\Desktop\pythonicfin\.env.sample","secret_key","25"
"C:\Users\<USER>\Desktop\pythonicfin\.env.sample","password","3"
"C:\Users\<USER>\Desktop\pythonicfin\.env.sample","credential","1, 5, 19, 23"
"C:\Users\<USER>\Desktop\pythonicfin\cleanup.py","credential","22"
"C:\Users\<USER>\Desktop\pythonicfin\ENV_SETUP.md","api_key","35, 38"
"C:\Users\<USER>\Desktop\pythonicfin\ENV_SETUP.md","password","16, 27"
"C:\Users\<USER>\Desktop\pythonicfin\ENV_SETUP.md","credential","3, 12, 14, 18, 25, 29, 44, 45, 49"
"C:\Users\<USER>\Desktop\pythonicfin\README.md","credential","52, 88"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\.env.sample","api_key","11, 14, 17, 24, 28"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\.env.sample","secret_key","25"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\.env.sample","password","3"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\.env.sample","credential","1, 5, 19, 23"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\cleanup.py","credential","22"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\ENV_SETUP.md","api_key","35, 38"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\ENV_SETUP.md","password","16, 27"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\ENV_SETUP.md","credential","3, 12, 14, 18, 25, 29, 44, 45, 49"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\README.md","credential","52, 88"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\test_chart_analysis.py","credential","63"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\ticker_data_v4.py","credential","29"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\stockdata\fundamentals.py","api_key","17"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\stockdata\fundamentals.py","apikey","28, 57, 228"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\ticker_analyzer\config.yaml","password","31"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\ticker_analyzer\config.yaml","credential","29, 30"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\ticker_analyzer\data_fetcher.py","credential","14, 18, 20, 49"
"C:\Users\<USER>\Desktop\pythonicfin\git-scan-temp\src\util\cryp_free_greed.py","api_key","16, 17"
"C:\Users\<USER>\Desktop\pythonicfin\memory-bank\progress.md","credential","32"
"C:\Users\<USER>\Desktop\pythonicfin\memory-bank\systemPatterns.md","credential","49"
"C:\Users\<USER>\Desktop\pythonicfin\memory-bank\techContext.md","password","35"
"C:\Users\<USER>\Desktop\pythonicfin\memory-bank\techContext.md","credential","17, 35, 44"
"C:\Users\<USER>\Desktop\pythonicfin\src\.env","api_key","3, 10, 13, 15"
"C:\Users\<USER>\Desktop\pythonicfin\src\.env","secret_key","14"
"C:\Users\<USER>\Desktop\pythonicfin\src\.env","credential","5, 9"
"C:\Users\<USER>\Desktop\pythonicfin\src\.env","OvDtfFpZaBhvf4mgKV9g7MSNrcxceFK4B0bfjStH","14"
"C:\Users\<USER>\Desktop\pythonicfin\src\test_chart_analysis.py","credential","63"
"C:\Users\<USER>\Desktop\pythonicfin\src\ticker_data_v4.py","credential","29"
"C:\Users\<USER>\Desktop\pythonicfin\src\stockdata\fundamentals.py","api_key","17"
"C:\Users\<USER>\Desktop\pythonicfin\src\stockdata\fundamentals.py","apikey","28, 57, 228"
"C:\Users\<USER>\Desktop\pythonicfin\src\ticker_analyzer\config.yaml","password","31"
"C:\Users\<USER>\Desktop\pythonicfin\src\ticker_analyzer\config.yaml","credential","29, 30"
"C:\Users\<USER>\Desktop\pythonicfin\src\ticker_analyzer\data_fetcher.py","credential","14, 18, 20, 49"
"C:\Users\<USER>\Desktop\pythonicfin\src\util\cryp_free_greed.py","api_key","16, 17"
