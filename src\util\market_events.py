from selenium import webdriver

# create a Firefox profile
firefox_profile = webdriver.FirefoxProfile()

# set the download directory for the Firefox profile
firefox_profile.set_preference("browser.download.folderList", 2)
firefox_profile.set_preference("browser.download.manager.showWhenStarting", False)
firefox_profile.set_preference("browser.download.dir", "/")
firefox_profile.set_preference("browser.helperApps.neverAsk.saveToDisk", "image/png")

# set up Firefox options to run headless
firefox_options = webdriver.FirefoxOptions()
firefox_options.add_argument('--headless')


# create a Firefox web driver
driver = webdriver.Firefox(firefox_profile=firefox_profile)

# navigate to the website
driver.get("https://www.forexfactory.com/calendar?month=this")

# wait for the page to load
driver.implicitly_wait(10)

# take a screenshot and save it as "screenshot.png"
driver.save_screenshot("events.png")

# # close the web driver
# driver.quit()
