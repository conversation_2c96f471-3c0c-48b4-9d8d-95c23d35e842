#!/bin/bash

# Script to run any of the PythonicFin tools

# Change to the script directory
cd "$(dirname "$0")/../.."

# Display usage information
function show_usage {
    echo "Usage: ./run_tool.sh [tool] [arguments]"
    echo ""
    echo "Available tools:"
    echo "  ticker [symbol]    - Run ticker analysis for the specified symbol"
    echo "  screener           - Run the market screener"
    echo "  enhanced           - Run enhanced chart analysis"
    echo ""
    echo "Examples:"
    echo "  ./run_tool.sh ticker AAPL"
    echo "  ./run_tool.sh screener"
    echo "  ./run_tool.sh enhanced"
}

# Check if a tool was specified
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

# Set environment variables from .env file
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Set PYTHONPATH to include the src directory
export PYTHONPATH="$(pwd)/src"

# Activate the Python virtual environment
source venv/bin/activate

# Run the specified tool
case "$1" in
    ticker)
        shift
        python -c "import sys; sys.path.append('src'); from ticker_analyzer.ticker_main import main; main()" "$@"
        ;;
    screener)
        python -c "import sys; sys.path.append('src'); from fidelity.scanner.intra_scanner import *"
        ;;
    enhanced)
        python -m fidelity.chart_analysis.enhanced_analysis
        ;;
    *)
        echo "Unknown tool: $1"
        show_usage
        exit 1
        ;;
esac
