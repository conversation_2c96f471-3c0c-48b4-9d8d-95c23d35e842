"""
Test script to verify that yfinance 0.2.54 works correctly after our modifications.
This script tests:
1. Basic stock data retrieval with yf.download()
2. Ticker object functionality
3. Index data retrieval
"""

import sys
import os

# Add src directory to Python path
sys.path.append('src')

import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from common.http_session import get_session
# Disable SSL verification to fix certificate issues
session = get_session('src/certs/my_ca_bundle.pem')

def test_yf_download():
    """Test basic yf.download() functionality."""
    print("Testing yf.download() with ticker 'AAPL'...")

    # Get stock data for Apple
    df = yf.download('AAPL', period='1mo',session=session)

    if df is not None and not df.empty:
        print("Success! Retrieved data for AAPL:")
        print(f"Shape: {df.shape}")
        print(f"Date range: {df.index.min()} to {df.index.max()}")
        print(f"Columns: {df.columns.tolist()}")
        print("\nFirst 5 rows:")
        print(df.head())
        return True
    else:
        print("Failed to retrieve data for AAPL")
        return False

def test_ticker_object():
    """Test Ticker object functionality."""
    print("\nTesting Ticker object with 'MSFT'...")

    # Create Ticker object for Microsoft
    msft = yf.Ticker('MSFT')

    # Test basic info
    info = msft.info
    if info:
        print("Success! Retrieved info for MSFT:")
        print(f"Company name: {info.get('shortName', 'N/A')}")
        print(f"Sector: {info.get('sector', 'N/A')}")
        print(f"Industry: {info.get('industry', 'N/A')}")

        # Test history method
        df = msft.history(period='1mo')
        if df is not None and not df.empty:
            print("\nSuccessfully retrieved history data:")
            print(f"Shape: {df.shape}")
            return True
        else:
            print("Failed to retrieve history data")
            return False
    else:
        print("Failed to retrieve info for MSFT")
        return False

def test_index_download():
    """Test downloading index data."""
    print("\nTesting index download with '^GSPC' (S&P 500)...")

    # Get S&P 500 data
    df = yf.download('^GSPC', period='1mo')

    if df is not None and not df.empty:
        print("Success! Retrieved data for S&P 500:")
        print(f"Shape: {df.shape}")
        print(f"Date range: {df.index.min()} to {df.index.max()}")
        return True
    else:
        print("Failed to retrieve data for S&P 500")
        return False

def run_all_tests():
    """Run all yfinance tests."""
    print("Testing yfinance 0.2.54 after modifications...")

    # Run all tests
    download_test = test_yf_download()
    ticker_test = test_ticker_object()
    index_test = test_index_download()

    # Print summary
    print("\n=== Test Summary ===")
    print(f"yf.download() test: {'PASSED' if download_test else 'FAILED'}")
    print(f"Ticker object test: {'PASSED' if ticker_test else 'FAILED'}")
    print(f"Index download test: {'PASSED' if index_test else 'FAILED'}")

    if download_test and ticker_test and index_test:
        print("\nAll tests PASSED! yfinance 0.2.54 is working correctly.")
        return True
    else:
        print("\nSome tests FAILED. Further investigation needed.")
        return False

if __name__ == "__main__":
    run_all_tests()
