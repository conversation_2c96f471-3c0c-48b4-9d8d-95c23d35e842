"""
Unit test for news functionality in ticker_analyzer.data_fetcher module.
Tests the ability to fetch news for specific tickers.
"""

import sys
import os
import unittest
import json
from io import StringIO
import contextlib

# Add src directory to Python path
sys.path.append('src')

# Import the function to test
from ticker_analyzer.data_fetcher import fetch_basic_info_and_news
import yfinance as yf
from common.http_session import get_session
session = get_session('src/certs/my_ca_bundle.pem')

def test_direct_yfinance_news(ticker_symbol):
    """Test direct yfinance news retrieval for a specific ticker."""
    print(f"\n=== Testing Direct yfinance News for {ticker_symbol} ===")

    try:
        ticker = yf.Ticker(ticker_symbol,session=session)
        news = ticker.get_news(count=5, tab="news")

        if news and isinstance(news, list) and len(news) > 0:
            print(f"SUCCESS: Found {len(news)} news items for {ticker_symbol}")

            # Print details of the first news item
            first_item = news[0]
            print("\nFirst news item details:")

            # Extract and print the title
            title = first_item.get('title', 'N/A')
            if 'content' in first_item and 'title' in first_item['content']:
                title = first_item['content']['title']
            print(f"Title: {title}")

            # Extract and print the publication date
            pub_date = "N/A"
            if 'content' in first_item and 'pubDate' in first_item['content']:
                pub_date = first_item['content']['pubDate']
            elif 'published_at' in first_item:
                pub_date = first_item['published_at']
            print(f"Publication Date: {pub_date}")

            # Extract and print the URL
            url = "#"
            if 'content' in first_item and 'clickThroughUrl' in first_item['content']:
                if 'url' in first_item['content']['clickThroughUrl']:
                    url = first_item['content']['clickThroughUrl']['url']
            elif 'link' in first_item:
                url = first_item['link']
            print(f"URL: {url}")

            return True
        else:
            print(f"WARNING: No news found for {ticker_symbol} using direct yfinance")
            return False
    except Exception as e:
        print(f"ERROR: Direct yfinance news retrieval failed with error: {e}")
        return False

def test_fetch_basic_info_and_news(ticker_symbol):
    """Test the fetch_basic_info_and_news function for a specific ticker."""
    print(f"\n=== Testing fetch_basic_info_and_news for {ticker_symbol} ===")

    # Capture stdout
    captured_output = StringIO()
    with contextlib.redirect_stdout(captured_output):
        fetch_basic_info_and_news(ticker_symbol)

    # Get the captured output
    output = captured_output.getvalue()

    # Check if news section exists
    if "--- Recent News (Yahoo) ---" in output:
        print("News section header found in output")

        # Check if we got actual news or the "No news found" message
        if "No recent news found via yfinance" in output:
            print(f"WARNING: No news found for {ticker_symbol} using fetch_basic_info_and_news")
            return False
        else:
            # Check for news items (they should start with "- ")
            news_lines = [line for line in output.split('\n') if line.strip().startswith("- ")]
            if len(news_lines) > 0:
                print(f"SUCCESS: Found {len(news_lines)} news items for {ticker_symbol}")

                # Print the first news item
                if news_lines:
                    print(f"First news item: {news_lines[0]}")

                return True
            else:
                print(f"WARNING: No news items found for {ticker_symbol} in the output")
                return False
    else:
        print("ERROR: News section header not found in output")
        return False

def main():
    """Main function to run the tests."""
    print("=== News Functionality Test ===")

    # Test TSLA first
    print("\n=== TESTING TESLA (TSLA) ===")
    direct_tsla_result = test_direct_yfinance_news("TSLA")
    wrapper_tsla_result = test_fetch_basic_info_and_news("TSLA")

    # Test a few more tickers
    other_tickers = ["AAPL", "MSFT", "GOOGL", "AMZN"]
    direct_results = []
    wrapper_results = []

    for ticker in other_tickers:
        print(f"\n=== TESTING {ticker} ===")
        direct_result = test_direct_yfinance_news(ticker)
        wrapper_result = test_fetch_basic_info_and_news(ticker)
        direct_results.append(direct_result)
        wrapper_results.append(wrapper_result)

    # Print summary
    print("\n=== TEST SUMMARY ===")
    print(f"TSLA Direct yfinance test: {'PASSED' if direct_tsla_result else 'FAILED'}")
    print(f"TSLA fetch_basic_info_and_news test: {'PASSED' if wrapper_tsla_result else 'FAILED'}")

    for i, ticker in enumerate(other_tickers):
        print(f"{ticker} Direct yfinance test: {'PASSED' if direct_results[i] else 'FAILED'}")
        print(f"{ticker} fetch_basic_info_and_news test: {'PASSED' if wrapper_results[i] else 'FAILED'}")

    # Overall result
    all_direct = direct_tsla_result and all(direct_results)
    all_wrapper = wrapper_tsla_result and all(wrapper_results)

    print("\n=== OVERALL RESULT ===")
    print(f"Direct yfinance tests: {'PASSED' if all_direct else 'FAILED'}")
    print(f"fetch_basic_info_and_news tests: {'PASSED' if all_wrapper else 'FAILED'}")

    if not all_direct and all_wrapper:
        print("\nNOTE: Direct tests failed but wrapper tests passed. This suggests the wrapper function is handling errors gracefully.")
    elif all_direct and not all_wrapper:
        print("\nWARNING: Direct tests passed but wrapper tests failed. This suggests an issue with the wrapper function.")
    elif not all_direct and not all_wrapper:
        print("\nWARNING: Both direct and wrapper tests failed. This suggests an issue with the yfinance library or network connectivity.")

if __name__ == "__main__":
    main()
