"""
Enhanced Bond Analysis Script

This script analyzes bond data with a focus on liquidity and bid-ask spread,
helping to identify bonds with good trading characteristics.

Key metrics analyzed:
1. Bid-Ask Spread - Tighter spreads indicate more liquid bonds
2. Quantity Available - Higher quantities indicate better liquidity
3. Yield metrics - For comparing potential returns
4. Credit Ratings - For assessing risk
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

# Configuration parameters - adjust these as needed
MAX_SPREAD = 2.00  # Maximum acceptable bid-ask spread
MIN_QUANTITY = 10  # Minimum quantity available
MIN_ASK_QUANTITY = 0  # Minimum required purchase quantity (the number in parentheses)
MAX_ASK_QUANTITY = 1000000  # Maximum required purchase quantity (the number in parentheses)
MIN_YIELD = 2.5    # Minimum yield to worst
MIN_RATING_SCORE = 5  # Minimum rating score (see rating_to_score function)
CALLABLE_FILTER = 'all'  # Filter for callable bonds: 'all', 'callable', 'non-callable'
CALL_PROTECTION_FILTER = 'all'  # Filter for call protection: 'all', 'protected', 'unprotected'

# Tax parameters
FEDERAL_TAX_RATE = 0.22  # 22% federal tax rate for high-income investors
STATE_TAX_RATE = 0.05  # 5% state tax rate (default: MA)
TAX_STATE = 'MA'  # Default: MA

def rating_to_score(rating):
    """Convert Moody's or S&P ratings to a numeric score.
    Higher score = better rating.
    """
    if pd.isna(rating) or rating == '--':
        return 0

    # Moody's ratings
    if rating.startswith('Aaa') or rating.startswith('AAA'):
        return 10
    elif rating.startswith('Aa1') or rating.startswith('AA+'):
        return 9
    elif rating.startswith('Aa2') or rating.startswith('AA'):
        return 8
    elif rating.startswith('Aa3') or rating.startswith('AA-'):
        return 7
    elif rating.startswith('A1') or rating.startswith('A+'):
        return 6
    elif rating.startswith('A2') or rating.startswith('A'):
        return 5
    elif rating.startswith('A3') or rating.startswith('A-'):
        return 4
    elif rating.startswith('Baa1') or rating.startswith('BBB+'):
        return 3
    elif rating.startswith('Baa2') or rating.startswith('BBB'):
        return 2
    elif rating.startswith('Baa3') or rating.startswith('BBB-'):
        return 1
    else:
        return 0  # Below investment grade or not rated

def parse_quantity(qty_str):
    """Parse quantity strings like '10(10)' to get the minimum quantity."""
    if pd.isna(qty_str) or qty_str == 'N/A(N/A)':
        return 0

    try:
        # Extract the first number from strings like "10(10)"
        first_num = qty_str.split('(')[0]
        return int(first_num) if first_num.isdigit() else 0
    except:
        return 0

def parse_ask_quantity(qty_str):
    """Parse ask quantity strings like '10(10)' to get the minimum required ask quantity (the number in parentheses)."""
    if pd.isna(qty_str) or qty_str == 'N/A(N/A)':
        return 0

    try:
        # Extract the number in parentheses from strings like "10(10)"
        if '(' in qty_str and ')' in qty_str:
            min_qty = qty_str.split('(')[1].split(')')[0]
            return int(min_qty) if min_qty.isdigit() else 0
        else:
            # If no parentheses, use the whole number
            return int(qty_str) if qty_str.isdigit() else 0
    except:
        return 0

def calculate_liquidity_score(row):
    """Calculate a liquidity score based on spread and quantity.
    Lower spread and higher quantity = higher score.
    """
    spread_score = 10 - min(row['Spread'] * 5, 10)  # Lower spread = higher score
    quantity_score = min(row['Quantity_Available'] / 5, 10)  # Higher quantity = higher score

    # Weight spread more heavily than quantity
    return (spread_score * 0.7) + (quantity_score * 0.3)

def is_tax_exempt(row, investor_state):
    """Determine if a bond is tax-exempt for a given state.

    Args:
        row: DataFrame row containing bond information
        investor_state: Two-letter state code of the investor

    Returns:
        tuple: (federal_exempt, state_exempt)
    """
    # Check for tax-exempt status in attributes
    attributes = str(row['Attributes']) if not pd.isna(row['Attributes']) else ''
    federal_exempt = 'TE' in attributes  # TE = Tax Exempt

    # State tax exemption depends on the bond's state matching the investor's state
    # Municipal bonds are typically exempt from state tax if issued in the investor's state
    bond_state = str(row['State']) if not pd.isna(row['State']) else ''
    state_exempt = (bond_state == investor_state) and federal_exempt

    return federal_exempt, state_exempt

def main():
    # Get the current script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Load the CSV file (replace with your file path)
    file_path = r"C:\Users\<USER>\Downloads\Fidelity_FixedIncome_SearchResults (13).csv"
    df = pd.read_csv(file_path)

    print(f"Loaded {len(df)} bonds from CSV file.")

    # Clean CUSIP formatting
    df['Cusip'] = df['Cusip'].str.extract(r'="(\w+)"')

    # Convert necessary columns to numeric
    df['Price Bid'] = pd.to_numeric(df['Price Bid'], errors='coerce')
    df['Price Ask'] = pd.to_numeric(df['Price Ask'], errors='coerce')
    df['Coupon'] = pd.to_numeric(df['Coupon'], errors='coerce')
    df['Ask Yield to Worst'] = pd.to_numeric(df['Ask Yield to Worst'], errors='coerce')
    df['Ask Yield to Maturity'] = pd.to_numeric(df['Ask Yield to Maturity'], errors='coerce')

    # Drop rows with missing critical info
    df = df.dropna(subset=['Price Ask', 'Coupon'])
    print(f"After removing rows with missing data: {len(df)} bonds.")

    # Calculate Spread
    df['Spread'] = df['Price Ask'] - df['Price Bid']
    # For rows where Price Bid is NaN, set a default high spread
    df.loc[df['Spread'].isna(), 'Spread'] = 5.0

    # Parse quantity information
    df['Quantity_Available'] = df['Quantity Ask(min)'].apply(parse_quantity)
    df['Ask_Quantity'] = df['Quantity Ask(min)'].apply(parse_ask_quantity)

    # Convert ratings to scores
    df['Moodys_Score'] = df["Moody's Rating"].apply(rating_to_score)
    df['SP_Score'] = df["S&P Rating"].apply(rating_to_score)
    df['Rating_Score'] = df[['Moodys_Score', 'SP_Score']].max(axis=1)

    # Set assumed face value
    face_value = 1000

    # Calculate financial metrics
    df['Price Paid ($)'] = (df['Price Ask'] / 100) * face_value
    df['Annual Coupon Income ($)'] = (df['Coupon'] / 100) * face_value
    df['Current Yield (%)'] = (df['Annual Coupon Income ($)'] / df['Price Paid ($)']) * 100

    # Calculate gain/loss to par at maturity
    df['Gain/Loss to Par ($)'] = face_value - df['Price Paid ($)']

    # Calculate years to maturity
    df['Maturity Date'] = pd.to_datetime(df['Maturity Date'], errors='coerce')
    today = pd.Timestamp.today()
    df['Years to Maturity'] = ((df['Maturity Date'] - today).dt.days / 365).round(2)

    # Process call date information
    df['Next Call Date'] = pd.to_datetime(df['Next Call Date'], errors='coerce')
    df['Is Callable'] = ~df['Next Call Date'].isna() & (df['Next Call Date'] != '--')

    # Process call protection information
    df['Has Call Protection'] = df['Attributes'].str.contains('CP', na=False)

    # Calculate years to call for callable bonds
    df['Years to Call'] = np.nan
    mask = df['Is Callable']
    df.loc[mask, 'Years to Call'] = ((df.loc[mask, 'Next Call Date'] - today).dt.days / 365).round(2)

    # Calculate call protection period
    df['Call Protection (Years)'] = df['Years to Call']

    # Calculate yield differential (YTM - YTW)
    df['Yield Differential'] = df['Ask Yield to Maturity'] - df['Ask Yield to Worst']

    # Determine if YTW is based on call date
    df['YTW Based on Call'] = (df['Yield Differential'] > 0.0001) & df['Is Callable']

    # Calculate call risk score (0-10, higher is better/lower risk)
    df['Call Risk Score'] = 10.0  # Default for non-callable bonds

    # For callable bonds, calculate based on call protection and yield differential
    callable_mask = df['Is Callable']

    # Call protection component (up to 7.5 points)
    # 0 years = 0 points, 5+ years = 7.5 points
    df.loc[callable_mask, 'Call Risk Score'] = np.minimum(df.loc[callable_mask, 'Call Protection (Years)'] * 1.5, 7.5)

    # Yield differential component (penalty up to 7.5 points)
    # 0% diff = 0 penalty, 2%+ diff = 7.5 point penalty
    yield_diff_penalty = np.minimum(df.loc[callable_mask, 'Yield Differential'] * 3.75, 7.5)
    df.loc[callable_mask, 'Call Risk Score'] -= yield_diff_penalty

    # Ensure score is between 0 and 10
    df['Call Risk Score'] = np.clip(df['Call Risk Score'], 0, 10)

    # Calculate $10,000 investment metrics
    df['Bonds per $10k'] = (10000 / df['Price Paid ($)']).apply(lambda x: int(x))
    df['Investment Cost ($)'] = df['Bonds per $10k'] * df['Price Paid ($)']
    df['Remaining Cash ($)'] = 10000 - df['Investment Cost ($)']
    df['Annual Income ($10k)'] = df['Bonds per $10k'] * df['Annual Coupon Income ($)']
    df['Total Interest ($10k)'] = df['Annual Income ($10k)'] * df['Years to Maturity']
    df['Principal at Maturity ($10k)'] = df['Bonds per $10k'] * face_value
    df['Capital Gain/Loss ($10k)'] = df['Bonds per $10k'] * df['Gain/Loss to Par ($)']
    df['Total Return ($10k)'] = df['Total Interest ($10k)'] + df['Capital Gain/Loss ($10k)']
    df['Total Return Rate ($10k)'] = (df['Total Return ($10k)'] / df['Investment Cost ($)'] * 100).round(2)

    # Calculate after-tax returns
    # Determine tax exemption status for each bond
    tax_status = df.apply(lambda row: is_tax_exempt(row, TAX_STATE), axis=1)
    df['Federal Tax Exempt'] = [x[0] for x in tax_status]
    df['State Tax Exempt'] = [x[1] for x in tax_status]

    # Calculate effective tax rates based on exemption status
    df['Effective Federal Tax Rate'] = df['Federal Tax Exempt'].apply(lambda x: 0 if x else FEDERAL_TAX_RATE)
    df['Effective State Tax Rate'] = df['State Tax Exempt'].apply(lambda x: 0 if x else STATE_TAX_RATE)
    df['Effective Total Tax Rate'] = df['Effective Federal Tax Rate'] + df['Effective State Tax Rate']

    # Calculate after-tax income
    df['After-Tax Annual Income ($10k)'] = df['Annual Income ($10k)'] * (1 - df['Effective Total Tax Rate'])
    df['After-Tax Total Interest ($10k)'] = df['After-Tax Annual Income ($10k)'] * df['Years to Maturity']

    # Capital gains are taxed at different rates, but for simplicity we'll use the same rate
    # In reality, capital gains might be taxed at lower rates
    df['After-Tax Capital Gain/Loss ($10k)'] = df['Capital Gain/Loss ($10k)'] * (1 - df['Effective Federal Tax Rate'])

    # Calculate total after-tax return
    df['After-Tax Total Return ($10k)'] = df['After-Tax Total Interest ($10k)'] + df['After-Tax Capital Gain/Loss ($10k)']
    df['After-Tax Return Rate ($10k)'] = (df['After-Tax Total Return ($10k)'] / df['Investment Cost ($)'] * 100).round(2)

    # Calculate annualized after-tax yield
    # Use the formula: (1 + total_return_rate/100)^(1/years) - 1
    df['Annualized After-Tax Yield (%)'] = (((1 + df['After-Tax Return Rate ($10k)']/100) ** (1/df['Years to Maturity'])) - 1) * 100
    df['Annualized After-Tax Yield (%)'] = df['Annualized After-Tax Yield (%)'].round(2)

    # Calculate annualized gain/loss
    df['Annualized Gain/Loss (%)'] = (df['Gain/Loss to Par ($)'] / df['Price Paid ($)'] / df['Years to Maturity'] * 100)

    # Calculate total return (annualized)
    df['Total Return (%)'] = df['Current Yield (%)'] + df['Annualized Gain/Loss (%)']

    # Calculate liquidity score
    df['Liquidity Score'] = df.apply(calculate_liquidity_score, axis=1)

    # Calculate combined score (liquidity + call risk + rating)
    df['Combined Score'] = (df['Liquidity Score'] * 0.5) + \
                          (df['Call Risk Score'] * 0.3) + \
                          (df['Rating_Score'] / 10 * 0.2 * 10)  # Scale rating to 0-10

    # Apply filters for liquidity and spread
    filter_conditions = [
        (df['Spread'] <= MAX_SPREAD),
        (df['Quantity_Available'] >= MIN_QUANTITY),
        (df['Ask_Quantity'] >= MIN_ASK_QUANTITY),
        (df['Ask_Quantity'] <= MAX_ASK_QUANTITY),
        (df['Ask Yield to Worst'] >= MIN_YIELD),
        (df['Rating_Score'] >= MIN_RATING_SCORE)
    ]

    # Add callable filter if specified
    if CALLABLE_FILTER == 'callable':
        filter_conditions.append(df['Is Callable'])
    elif CALLABLE_FILTER == 'non-callable':
        filter_conditions.append(~df['Is Callable'])

    # Add call protection filter if specified
    if CALL_PROTECTION_FILTER == 'protected':
        filter_conditions.append(df['Has Call Protection'])
    elif CALL_PROTECTION_FILTER == 'unprotected':
        filter_conditions.append(~df['Has Call Protection'])

    # Apply all filters
    filtered_df = df[np.all(np.column_stack(filter_conditions), axis=1)]

    print(f"After applying filters: {len(filtered_df)} bonds.")

    # Sort by combined score (descending) and yield (descending)
    filtered_df = filtered_df.sort_values(by=['Combined Score', 'Ask Yield to Worst'], ascending=[False, False])

    # Select final columns to export
    columns_to_export = [
        'Cusip', 'Description', 'Coupon', 'Maturity Date', 'Years to Maturity',
        'Price Bid', 'Price Ask', 'Spread', 'Quantity_Available', 'Ask_Quantity', 'Liquidity Score',
        'Combined Score',  # Overall score including liquidity, call risk, and rating
        'Ask Yield to Worst', 'Ask Yield to Maturity', 'Current Yield (%)', 'Total Return (%)',
        'Price Paid ($)', 'Annual Coupon Income ($)', 'Gain/Loss to Par ($)',
        # Call risk metrics
        'Is Callable', 'Has Call Protection', 'Next Call Date', 'Years to Call', 'Call Protection (Years)',
        'Yield Differential', 'YTW Based on Call', 'Call Risk Score',
        # $10,000 investment metrics
        'Bonds per $10k', 'Investment Cost ($)', 'Remaining Cash ($)',
        'Annual Income ($10k)', 'Total Interest ($10k)',
        'Principal at Maturity ($10k)', 'Capital Gain/Loss ($10k)', 'Total Return ($10k)', 'Total Return Rate ($10k)',
        # Tax status
        'Federal Tax Exempt', 'State Tax Exempt', 'Effective Total Tax Rate',
        # After-tax metrics
        'After-Tax Annual Income ($10k)', 'After-Tax Total Interest ($10k)',
        'After-Tax Capital Gain/Loss ($10k)', 'After-Tax Total Return ($10k)', 'After-Tax Return Rate ($10k)',
        'Annualized After-Tax Yield (%)',
        # Ratings
        "Moody's Rating", "S&P Rating", 'Rating_Score',
        'State', 'Attributes'
    ]

    output = filtered_df[columns_to_export]

    # Export final CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(script_dir, f"liquid_bonds_{timestamp}.csv")
    output.to_csv(output_path, index=False)

    # Also create an Excel file with formatting
    excel_path = os.path.join(script_dir, f"liquid_bonds_{timestamp}.xlsx")

    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            output.to_excel(writer, index=False, sheet_name='Liquid Bonds')

            # Get the workbook and the worksheet
            workbook = writer.book
            worksheet = writer.sheets['Liquid Bonds']

            # Add a summary sheet
            summary_data = {
                'Metric': [
                    'Total Bonds Analyzed',
                    'Bonds Meeting Criteria',
                    'Average Yield to Worst',
                    'Average Spread',
                    'Average Liquidity Score',
                    'Average Combined Score',
                    'Highest Yield to Worst',
                    'Lowest Spread',
                    'Highest Liquidity Score',
                    'Highest Combined Score',
                    '--- Call Risk Metrics ---',
                    'Callable Bonds (%)',
                    'Average Call Protection (Years)',
                    'Average Call Risk Score',
                    'Bonds with YTW Based on Call (%)',
                    'Average Yield Differential',
                    '--- $10,000 Investment Metrics ---',
                    'Average Bonds per $10k',
                    'Average Annual Income',
                    'Average Total Return',
                    'Average Total Return Rate (%)',
                    'Highest Annual Income',
                    'Highest Total Return',
                    '--- After-Tax Metrics (MA) ---',
                    'Tax-Exempt Bonds (%)',
                    'Average After-Tax Annual Income',
                    'Average After-Tax Total Return',
                    'Average After-Tax Return Rate (%)',
                    'Average Annualized After-Tax Yield (%)',
                    'Highest After-Tax Annual Income',
                    'Highest After-Tax Total Return',
                    'Highest Annualized After-Tax Yield (%)'
                ],
                'Value': [
                    len(df),
                    len(filtered_df),
                    filtered_df['Ask Yield to Worst'].mean(),
                    filtered_df['Spread'].mean(),
                    filtered_df['Liquidity Score'].mean(),
                    filtered_df['Combined Score'].mean(),
                    filtered_df['Ask Yield to Worst'].max(),
                    filtered_df['Spread'].min(),
                    filtered_df['Liquidity Score'].max(),
                    filtered_df['Combined Score'].max(),
                    '',
                    filtered_df['Is Callable'].mean() * 100,  # Percentage of callable bonds
                    filtered_df['Call Protection (Years)'].mean(),
                    filtered_df['Call Risk Score'].mean(),
                    filtered_df['YTW Based on Call'].mean() * 100,  # Percentage with YTW based on call
                    filtered_df['Yield Differential'].mean(),
                    '',
                    filtered_df['Bonds per $10k'].mean(),
                    filtered_df['Annual Income ($10k)'].mean(),
                    filtered_df['Total Return ($10k)'].mean(),
                    filtered_df['Total Return Rate ($10k)'].mean(),
                    filtered_df['Annual Income ($10k)'].max(),
                    filtered_df['Total Return ($10k)'].max(),
                    '',
                    filtered_df['Federal Tax Exempt'].mean() * 100,  # Percentage of tax-exempt bonds
                    filtered_df['After-Tax Annual Income ($10k)'].mean(),
                    filtered_df['After-Tax Total Return ($10k)'].mean(),
                    filtered_df['After-Tax Return Rate ($10k)'].mean(),
                    filtered_df['Annualized After-Tax Yield (%)'].mean(),
                    filtered_df['After-Tax Annual Income ($10k)'].max(),
                    filtered_df['After-Tax Total Return ($10k)'].max(),
                    filtered_df['Annualized After-Tax Yield (%)'].max()
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, index=False, sheet_name='Summary')

        print(f"Analysis complete. Exported to {output_path} and {excel_path}")

    except Exception as e:
        print(f"Error creating Excel file: {e}")
        print(f"CSV file was still created at {output_path}")

if __name__ == "__main__":
    main()
