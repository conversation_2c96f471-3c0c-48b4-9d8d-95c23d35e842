#!/bin/bash

# Change to the script directory
cd "$(dirname "$0")/../.."

# Set environment variables from .env file
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Set PYTHONPATH to include the src directory
export PYTHONPATH="$(pwd)/src"

# Activate the Python virtual environment and run the standalone download_stock_charts script
source venv/bin/activate && python src/standalone_download_stock_charts.py
