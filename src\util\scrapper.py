import aiohttp
import io
from distutils.log import error
from bs4 import BeautifulSoup
from numpy import float64
import pandas as pd
import datetime
import time
import requests
from common.http_session import get_session
import matplotlib.pyplot as plt
from stockdata.data_source import getStockDataV3
from util.util import findExchange, findLastWed
import docx
import os
import diskcache as dc
cache = dc.Cache('tmp')

headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36'}


HEADERS = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9',
    'dnt': '1',
    'origin': 'https://www.cboe.com',
    'priority': 'u=1, i',
    'referer': 'https://www.cboe.com/',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'sec-gpc': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with': 'XMLHttpRequest'
}

import pandas_market_calendars as mcal
def is_market_holiday(date_str):
    """
    Check if a given date is a market holiday or weekend.

    Parameters:
    - date_str: The date as a string in 'MM/DD/YYYY' format.

    Returns:
    - True if the date is a holiday or weekend, False otherwise.
    """
    # Parse the input date string using the specified format
    date = datetime.datetime.strptime(date_str, '%m/%d/%Y').date()

    # Get the NYSE calendar
    nyse = mcal.get_calendar('NYSE')

    # Get valid trading days within the year of the given date
    valid_days = nyse.valid_days(start_date=date.replace(month=1, day=1),
                                 end_date=date.replace(month=12, day=31))

    # Convert valid trading days to a set of dates for easy checking
    valid_dates = set(valid_days.date)

    # Check if the date is in the valid_dates
    return date not in valid_dates


def scrap_and_add_PCR(curr_date):
    existing_df = pd.read_csv('src/data/PC_Ratio.csv')
    val = existing_df['Date'].tolist()
    formatted_date = curr_date.strftime('%m/%d/%Y')
    if formatted_date not in val and not is_market_holiday(formatted_date):
        base_url = 'https://cdn.cboe.com/data/us/options/market_statistics/daily/'
        date_str = curr_date.strftime('%Y-%m-%d')

        url = f"{base_url}{date_str}_daily_options"

        print(f"Fetching data for: {date_str}")

        try:
            response = requests.get(url, headers=HEADERS)
            response.raise_for_status()

            data = response.json()
            ratios = data.get('ratios', [])

            # Map the names to ensure ordering in the output
            ratio_mapping = {
                "TOTAL PUT/CALL RATIO": "",
                "INDEX PUT/CALL RATIO": "",
                "EXCHANGE TRADED PRODUCTS PUT/CALL RATIO": "",
                "EQUITY PUT/CALL RATIO": "",
                "CBOE VOLATILITY INDEX (VIX) PUT/CALL RATIO": "",
                "SPX + SPXW PUT/CALL RATIO": "",
                "OEX PUT/CALL RATIO": "",
                "MRUT PUT/CALL RATIO": ""
            }

            # Fill ratio_mapping with the true values from the data, if available
            for item in ratios:
                name = item.get('name')
                value = item.get('value')
                if name in ratio_mapping:
                    ratio_mapping[name] = value

            # Create a row with the formatted date and the extracted ratios
            row = [formatted_date] + [ratio_mapping[key] for key in ratio_mapping]

            # Append row to CSV
            df = pd.DataFrame([row], columns=['Date'] + list(ratio_mapping.keys()))
            df.to_csv('src/data/PC_Ratio.csv', mode='a', index=False, header=False)
            print(f"Data for {date_str} has been processed and saved.")

        except Exception as e:
            print(f"An error occurred while processing {date_str}: {e}")


def getCurrentPCEData():
    df_list = pd.read_html(
        "https://www.cboe.com/us/options/market_statistics/#current-stats?mkt=cone")

    if not df_list[4].empty:
        df_ratio_data = df_list[4]
        df_ratio_data = df_ratio_data.dropna()
        df = pd.DataFrame(df_ratio_data)
        df['P/C'] = (df_ratio_data['PUTS']/df_ratio_data['CALLS'])

        return df.iloc[-1, -1]

# print(getCurrentPCEData())





def getPCRatios():
    base = datetime.date.today() - datetime.timedelta(days=1)

    date_list = [base - datetime.timedelta(days=x) for x in range(20)]

    for currDate in date_list:
        scrap_and_add_PCR(currDate)


async def squeezeMetrics(session: aiohttp.ClientSession):
    """
    Fetches squeeze metrics asynchronously.

    Args:
        session: An aiohttp.ClientSession instance.

    Returns:
        pandas.DataFrame: DataFrame containing squeeze metrics.
    """
    try:
        url = 'https://squeezemetrics.com/monitor/static/DIX.csv?_t=' + str(time.time())
        async with session.get(url) as response:
            response.raise_for_status()
            content = await response.text()
            exisiting_df = pd.read_csv(io.StringIO(content))
            return exisiting_df
    except aiohttp.ClientError as e:
        print(f'Error fetching squeeze metrics: {e}')
        return pd.DataFrame()
    except Exception as e:
        print(f'An unexpected error occurred in squeezeMetrics: {e}')
        return pd.DataFrame()


async def CNNFearGreed(session: aiohttp.ClientSession):
    """
    Get the current CNN Fear & Greed Index value asynchronously.

    Args:
        session: An aiohttp.ClientSession instance.

    Returns:
        float or int: The current Fear & Greed Index value (0-100)
                     Returns 0 if there's an error.
    """
    try:
        url = 'https://production.dataviz.cnn.io/index/fearandgreed/graphdata'
        # Add proper headers to avoid 418 error
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site'
        }
        async with session.get(url, headers=headers) as response:
            response.raise_for_status()
            jsonData = await response.json()
            val = jsonData["fear_and_greed"]["score"]
            return val
    except aiohttp.ClientError as e:
        print(f'Error in CNN Fear and Greed: {e}')
        return 50  # Return a neutral score on error
    except Exception as e:
        print(f'An unexpected error occurred in CNN Fear and Greed: {e}')
        return 50  # Return a neutral score on error

#print(CNNFearGreed())


def getIndexListMultpl():
    url = 'https://www.multpl.com/sitemap'
    page = requests.get(url)

    soup = BeautifulSoup(page.text, 'html.parser')
    li = soup.find_all('a', href=True)
    valList = []
    for el in li:
        valList.append(el['href'])
    dict = {'title': valList}
    df = pd.DataFrame(dict)
    df.drop_duplicates(subset=None, keep='first', inplace=True)
    df.to_csv('src/data/multpl.csv')


def getDataFromMultpl():
    df = pd.read_csv('src/data/multpl.csv')
    for val in df['title'].to_list():
        genrateReports(val)


def genrateReports(val):
    try:
        url = 'https://www.multpl.com'+val+'/table/by-month'
        print(url)
        df_list = pd.read_html(url)
        df_list = df_list[0]
        #print(df_list)
        df_list.rename(columns={"Yield Value": "Value Value"}, inplace="True")
        df_list.rename(columns={"Price Value": "Value Value"}, inplace="True")
        df_list.rename(columns={"CPI Value": "Value Value"}, inplace="True")
        df_list['Date'] = pd.to_datetime(df_list['Date'])

        if df_list['Value Value'].dtypes != float64:

            df_list['Value Value'] = df_list['Value Value'].str.replace(
                'estimate', '')
            df_list['Value Value'] = df_list['Value Value'].str.replace('%', '')
            df_list['Value Value'] = df_list['Value Value'].str.replace(
                'trillion', '')
            df_list['Value Value'] = df_list['Value Value'].str.replace(
                'billion', '')
            df_list['Value Value'] = pd.to_numeric(df_list['Value Value'])
        df_list['Mean'] = df_list['Value Value'].mean()
        df_list['1sd'] = df_list['Value Value'].mean() + \
            df_list['Value Value'].std()
        df_list['-1sd'] = df_list['Value Value'].mean() - \
            df_list['Value Value'].std()
        plt.title(val)
        plt.plot(df_list['Date'], df_list['Mean'])
        plt.plot(df_list['Date'], df_list['1sd'])
        plt.plot(df_list['Date'], df_list['Value Value'])
        # Use relative path for saving figures
        import os
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis', 'macro')
        os.makedirs(data_dir, exist_ok=True)
        plt.savefig(os.path.join(data_dir, val + '.jpeg'))
        plt.close()

    #plt.show()
    except:
        print("Error occured for", val)


def scrapEarnings(ticker, exchange):
    header = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.119 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    url = 'https://www.marketbeat.com/stocks/'+exchange+'/'+ticker+'/earnings/'

    print(url)

    response = requests.get(url, headers=header)

    try:
        df = pd.read_html(response.text)
        return df
    except :
        #try diff exchanges if not found
        if exchange=="NYSE":
            exchange = "NASDAQ"
        else :
            exchange = 'NYSE'

        url = 'https://www.marketbeat.com/stocks/'+exchange+'/'+ticker+'/earnings/'

        print(url)

        response = requests.get(url, headers=header)

        df = pd.read_html(response.text)

        return df


# scrapEarnings('NVDA','NASDAQ')


def institutionalHoldings(ticker):
    exchange = findExchange(ticker)
    header = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.119 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    url = 'https://www.marketbeat.com/stocks/' + \
        exchange+'/'+ticker+'/institutional-ownership/'
    response = requests.get(url, headers=header)

    df = pd.read_html(response.text)[0].replace({'%': ''}, regex=True)
    df['Reporting Date'] = pd.to_datetime(
        df['Reporting Date'], errors='coerce')
    df = df.dropna(subset=['Reporting Date'])
    df = df.sort_values(by=['Ownership in Company', 'Reporting Date'],
                        ascending=False)
    # print(df[['Reporting Date', 'Hedge Fund', 'Shares Held', 'Market Value',
    #       'Quarterly Change in Shares', 'Ownership in Company']].head(5))
    return df[['Reporting Date', 'Hedge Fund', 'Shares Held', 'Market Value',
               'Quarterly Change in Shares', 'Ownership in Company']].head(10)


#ticker = 'HGV'
# print(institutionalHoldings(ticker))


# https://fintel.io/somf/us/FOCS

# https://fintel.io/soe/us/fb


#print(fundHoldings('AMD'))
#https://finance.yahoo.com/quote/AMD/holders
#supress warnings
import warnings
warnings.filterwarnings("ignore")

async def fundHoldingsYahoo(session: aiohttp.ClientSession, ticker: str):
    """
    Get institutional ownership data for a ticker from MarketBeat asynchronously.

    Args:
        session: An aiohttp.ClientSession instance.
        ticker (str): The stock ticker symbol

    Returns:
        pandas.DataFrame: DataFrame containing institutional ownership data
    """
    try:
        exchange = findExchange(ticker)
    except Exception as e:
        print(f"Error finding exchange: {e}")
        print("Defaulting to NASDAQ")
        exchange = "NASDAQ"

    header = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    url = f'https://www.marketbeat.com/stocks/{exchange}/{ticker}/institutional-ownership/'
    print(f"Fetching institutional ownership data from: {url}")

    try:
        async with session.get(url, headers=header) as response:
            response.raise_for_status()
            response_text = await response.text()
            dfs = pd.read_html(io.StringIO(response_text))

            if len(dfs) > 0:
                for i, df in enumerate(dfs):
                    if 'Reporting Date' in df.columns and ('Hedge Fund' in df.columns or 'Major Shareholder Name' in df.columns):
                        df = df.replace({'%': ''}, regex=True)
                        df['Reporting Date'] = pd.to_datetime(df['Reporting Date'], errors='coerce')
                        df = df.dropna(subset=['Reporting Date'])
                        df = df.sort_values(by=['Ownership in Company', 'Reporting Date'], ascending=False)
                        shareholder_col = 'Major Shareholder Name' if 'Major Shareholder Name' in df.columns else 'Hedge Fund'
                        return df[[shareholder_col, 'Shares Held', 'Market Value',
                                  'Quarterly Change in Shares', 'Ownership in Company']].head(10)
                print(f"Found {len(dfs)} tables, but none matched the expected format. Returning first table.")
                return dfs[0]
            else:
                print("No tables found in the response")
                return pd.DataFrame()  # Return empty DataFrame

    except aiohttp.ClientError as e:
        print(f"Failed to retrieve data: {e}")
        # Try with a different exchange if the first one failed
        if exchange == "NYSE":
            exchange = "NASDAQ"
        else:
            exchange = "NYSE"

        url = f'https://www.marketbeat.com/stocks/{exchange}/{ticker}/institutional-ownership/'
        print(f"Trying with different exchange: {url}")

        try:
            async with session.get(url, headers=header) as response:
                response.raise_for_status()
                response_text = await response.text()
                dfs = pd.read_html(io.StringIO(response_text))
                if len(dfs) > 0:
                    for i, df in enumerate(dfs):
                        if 'Reporting Date' in df.columns and ('Hedge Fund' in df.columns or 'Major Shareholder Name' in df.columns):
                            df = df.replace({'%': ''}, regex=True)
                            df['Reporting Date'] = pd.to_datetime(df['Reporting Date'], errors='coerce')
                            df = df.dropna(subset=['Reporting Date'])
                            df = df.sort_values(by=['Ownership in Company', 'Reporting Date'], ascending=False)
                            shareholder_col = 'Major Shareholder Name' if 'Major Shareholder Name' in df.columns else 'Hedge Fund'
                            return df[[shareholder_col, 'Shares Held', 'Market Value',
                                      'Quarterly Change in Shares', 'Ownership in Company']].head(10)
                    return dfs[0]
                return pd.DataFrame()
        except aiohttp.ClientError as e:
            print(f"Error retrieving institutional ownership data (second attempt): {e}")
            return pd.DataFrame()
    except Exception as e:
        print(f"An unexpected error occurred in fundHoldingsYahoo: {e}")
        return pd.DataFrame()



#https://www.etf.com/stock/PDD

async def etfHoldingsETFDBdotCOM(session: aiohttp.ClientSession, ticker: str):
    """
    Fetches ETF holdings from ETFDB.com asynchronously.

    Args:
        session: An aiohttp.ClientSession instance.
        ticker (str): The ETF ticker symbol.

    Returns:
        pandas.DataFrame: DataFrame containing ETF holdings.
    """
    header = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.119 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    url = f'https://etfdb.com/stock/{ticker}/'
    try:
        async with session.get(url, headers=header) as response:
            response.raise_for_status()
            response_text = await response.text()
            df = pd.read_html(io.StringIO(response_text))

            result_df = df[0].head(20)
            if any(" " in col for col in result_df.columns):
                clean_columns = [col.split(" ")[0] for col in result_df.columns]
                result_df.columns = clean_columns
            return result_df
    except aiohttp.ClientError as e:
        print(f"Error fetching ETF holdings: {e}")
        return pd.DataFrame()
    except Exception as e:
        print(f"An unexpected error occurred in etfHoldingsETFDBdotCOM: {e}")
        return pd.DataFrame()

def convert_string_to_float(string):
  try:
    return float(string.replace(",", ""))
  except ValueError:
    raise ValueError("Could not convert string to float: {}".format(string))


def stockVolData():
  # Read the CSV file into a DataFrame
  url = "https://www.nyse.com/publicdocs/nyse/US_Equities_Volumes.csv"
  df = pd.read_csv(url)

  # Convert the Trade Date column to a date
  df["Trade Date"] = pd.to_datetime(df["Trade Date"], format="%Y%m%d")

  # Convert the string column to a float column
  df["Consolidated Tape A"] = df["Consolidated Tape A"].apply(convert_string_to_float)

  df["Consolidated Tape B"] = df["Consolidated Tape B"].apply(convert_string_to_float)

  df["Consolidated Tape C"] = df["Consolidated Tape C"].apply(convert_string_to_float)

  # Plot the data for Consolidated Tape A
  plt.plot(df["Trade Date"].values, df["Consolidated Tape A"].values, label="Consolidated Tape A")
  plt.xlabel("Trade Date")
  plt.ylabel("Price")
  plt.title("Consolidated Tape A")
  plt.legend()
  plt.show()

  # Plot the data for Consolidated Tape B
  plt.plot(df["Trade Date"].values, df["Consolidated Tape B"].values, label="Consolidated Tape B")
  plt.xlabel("Trade Date")
  plt.ylabel("Price")
  plt.title("Consolidated Tape B")
  plt.legend()
  plt.show()

  # Plot the data for Consolidated Tape C
  plt.plot(df["Trade Date"].values, df["Consolidated Tape C"].values, label="Consolidated Tape C")
  plt.xlabel("Trade Date")
  plt.ylabel("Price")
  plt.title("Consolidated Tape C")
  plt.legend()
  plt.show()

def NAAIMExp():
    today = datetime.datetime.today()

    url = 'https://www.naaim.org/wp-content/uploads/' + \
          str(today.year) + '/' + str(today.month).zfill(2) + \
          '/USE_Data-since-Inception_' + str(findLastWed()) + '.xlsx'

    headers = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    response = requests.get(url, headers=headers)  # Send the request with headers

    if response.status_code == 200:
        # Use the content of the response to read the Excel file
        exisiting_df = pd.read_excel(response.content).head(150)
        ax = plt.gca()
        exisiting_df.plot(kind='line', x='Date', y='Mean/Average', ax=ax)
        plt.show()
        return exisiting_df
    else:
        print(f"Failed to download file: {response.status_code}")
        return None
#NAAIMExp()

def topFunds():
    # https://money.usnews.com/funds/mutual-funds/rankings/large-growth
    #exchange = findExchange(ticker)
    header = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.119 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    url = 'https://money.usnews.com/funds/mutual-funds/rankings/large-growth'
    r = requests.get(url, headers=header)
    print(r.text)

def downloadCharts(tickers,docName):
    doc = docx.Document()
    baseUrl = 'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i=t8442194560c&r=tdy'
    fileName = 'images.docx'
    if docName:
        fileName = docName+'.docx'
    for  xyz in tickers:
      df  = getStockDataV3(xyz)
      #print(df)
      if  (df.size > 0
           #and df['slope_5'][0] > 0 and df['slope_10'][0] > 0 -- these slopes are not good to work with
           and df['slope_50'][0] > 0
           and df['slope_200'][0] > 0
           and df['150'][0] > df['200'][0] # commenting out sort of distortion eg) $SLM
           and df['50'][0] > df['150'][0]
           and df['50'][0] > df['200'][0]):
          url = baseUrl.replace('tdy', str(time.time())).replace('ticker', xyz)
          print(url)
          # Create charts directory if it doesn't exist
          charts_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'charts')
          os.makedirs(charts_dir, exist_ok=True)

          # Full path to the image file
          image_path = os.path.join(charts_dir, f"images{xyz}.png")

          with open(image_path, 'wb') as handle:
             response = requests.get(url, headers=headers)
             if not response.ok:
               print(response)
             for block in response.iter_content(1024):
              if not block:
               break
              handle.write(block)
          try:
              # Use the same image path as defined above
              doc.add_picture(image_path)
          except :
              print("Error in getting image for " + xyz)
          doc.save(fileName)
          os.remove(image_path)
          time.sleep(2)
      else:
         print("not downloading:", xyz)

    os.startfile(fileName)

def downloadCharts2(tickers,docName):
    doc = docx.Document()
    baseUrl = 'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=1&mn=6&dy=0&i=t8442194560c&r=tdy'
    fileName = 'images.docx'
    if docName:
        fileName = docName+'.docx'
    for  xyz in tickers:
      df  = getStockDataV3(xyz)
      #print(df)
      if  df.size > 0 and df['ext_20'][0] < 5 and df['ext_50'][0] < 30 and df['ext_200'][0] < 50:
          url = baseUrl.replace('tdy', str(time.time())).replace('ticker', xyz)
          print(url)
          with open(str('src/data/images'+str(xyz)) + '.png', 'wb') as handle:
             response = requests.get(url, headers=headers)
             if not response.ok:
               print(response)
             for block in response.iter_content(1024):
              if not block:
               break
              handle.write(block)
          try:
              doc.add_picture(str('src/data/images'+str(xyz)) + '.png')
          except :
              print("Error in getting image for " + xyz)
          doc.save(fileName)
          os.remove(str('src/data/images'+str(xyz)) + '.png')
          time.sleep(2)
      else:
         print("not downloading:", xyz)

    os.startfile(fileName)

# print(NAAIMExp())
# print(topFunds())
#downloadCharts(['AAPL'],'test')

def downloadChartsFidelity(tickers,docName):
    dailyId = input("Enter the daily chart ID: ")
    wklyId = input("Enter the weekly chart ID: ")
    doc = docx.Document()
    baseUrl = 'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=1&mn=6&dy=0&i=' + dailyId + '&r=tdy'
    baseUrl1 = 'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=5&mn=0&dy=0&i=' + wklyId + '&r=tdy'
    fileName = 'images.docx'
    if docName:
        fileName = docName+'.docx'
    for  xyz in tickers:
      df  = getStockDataV3(xyz)
      #print(df)
      if  df.size > 0 and df['slope_50'][0] > 0 and df['slope_200'][0] > 0:
          url = baseUrl.replace('tdy', str(time.time())).replace('ticker', xyz)
          print(url)
          with open(str('src/data/images'+str(xyz)) + '.png', 'wb') as handle:
             response = requests.get(url, headers=headers)
             if not response.ok:
               print(response)
             for block in response.iter_content(1024):
              if not block:
               break
              handle.write(block)
          try:
              doc.add_picture(str('src/data/images'+str(xyz)) + '.png')
          except :
              print("Error in getting image for " + xyz)
          doc.save(fileName)
          os.remove(str('src/data/images'+str(xyz )) + '.png')


          url1 = baseUrl1.replace('tdy', str(time.time())).replace('ticker', xyz)
          print(url1)
          with open(str('src/data/images'+str(xyz +"_week")) + '.png', 'wb') as handle:
             response = requests.get(url1, headers=headers)
             if not response.ok:
               print(response)
             for block in response.iter_content(1024):
              if not block:
               break
              handle.write(block)
          try:
              doc.add_picture(str('src/data/images'+str(xyz + "_week")) + '.png')
          except :
              print("Error in getting image for " + xyz + "_week")
          doc.save(fileName)
          os.remove(str('src/data/images'+str(xyz + "_week")) + '.png')
          time.sleep(1)
      else:
         print("not downloading:", xyz)

    os.startfile(fileName)

def  downloadChartsFidelityV2(screenerdf,daily_id,wkly_id,docName):
    doc = docx.Document()
    baseUrl = 'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i='+daily_id+'&r=tdy'
    baseUrl1 = 'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=5&mn=0&dy=0&i='+wkly_id+'&r=tdy'
    fileName = 'images.docx'
    if docName:
        fileName = docName+'.docx'
    excluded_tickers = []
    for  index, row in screenerdf.iterrows():
      xyz = ticker = row['Symbol']
      df  = getStockDataV3(xyz)
      sector = row['Sector']
      industry = row['Industry']
      sub_industry = row['Sub-Industry']
      beta = row['Beta (1 Year Annualized)']
      std_dev = row['Standard Deviation (1 Yr Annualized)']
      forward_eps_growth = row['Forward EPS Long Term Growth (3-5 Yrs)']
      eps_growth_last_qtr = row['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)']
      eps_growth_ttm_vs_prior_ttm = row['EPS Growth (TTM vs Prior TTM)']
      eps_growth_3_year = row['EPS Growth (3 Year History)']
      eps_growth_5_year = row['EPS Growth (5 Year Historical)']
      eps_growth_proj_next_yr_vs_this_yr = row['EPS Growth (Proj Next Yr vs. This Yr)']
      revenue_growth_ttm_vs_prior_ttm = row['Revenue Growth (TTM vs. Prior TTM)']
      revenue_growth_last_qtr = row['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)']
      equity_summary_score = row['Equity Summary Score (ESS) from LSEG StarMine']

      if  (df.size > 0
           #and df['slope_5'][0] > 0 and df['slope_10'][0] > 0 -- these slopes are not good to work with
           and df['slope_50'][0] > 0
           and df['slope_200'][0] > 0
           and df['UDRatio'][0] > 0.75
           and df['150'][0] > df['200'][0] # commenting out sort of distortion eg) $SLM
           and df['50'][0] > df['150'][0]
           and df['50'][0] > df['200'][0]
           and df['$VolumeM'][0] >= 25) :# $Vol > 25 million
          url = baseUrl.replace('tdy', str(time.time())).replace('ticker', xyz)
          print(url)
          with open(str('src/data/images'+str(xyz)) + '.png', 'wb') as handle:
             response = requests.get(url, headers=headers)
             if not response.ok:
               print(response)
             for block in response.iter_content(1024):
              if not block:
               break
              handle.write(block)
          try:
              doc.add_picture(str('src/data/images'+str(xyz)) + '.png')
              # Create a one-row table
              # Create a vertical table
              create_vertical_table(doc, sector, industry, sub_industry, beta, std_dev, forward_eps_growth,
                               eps_growth_last_qtr, eps_growth_ttm_vs_prior_ttm, eps_growth_3_year, eps_growth_5_year,
                               eps_growth_proj_next_yr_vs_this_yr, revenue_growth_ttm_vs_prior_ttm,
                               revenue_growth_last_qtr, equity_summary_score,round(df['ADRP'][0],2))
          except error:
              print(error)
              print("Error in getting image for " + xyz)
          doc.save(fileName)
          os.remove(str('src/data/images'+str(xyz )) + '.png')

          url1 = baseUrl1.replace('tdy', str(time.time())).replace('ticker', xyz)
          print(url1)
          with open(str('src/data/images'+str(xyz +"_week")) + '.png', 'wb') as handle:
             response = requests.get(url1, headers=headers)
             if not response.ok:
               print(response)
             for block in response.iter_content(1024):
              if not block:
               break
              handle.write(block)
          try:
              doc.add_picture(str('src/data/images'+str(xyz + "_week")) + '.png')
          except :
              print("Error in getting image for " + xyz + "_week")
          doc.save(fileName)
          os.remove(str('src/data/images'+str(xyz + "_week")) + '.png')
          time.sleep(1)
      else:
         print("not downloading:", xyz)
         exclusion_reason = []
         if df.size == 0:
             exclusion_reason.append("DataFrame is empty")
         if df['slope_50'][0] <= 0:
             exclusion_reason.append("slope_50 not > 0")
         if df['slope_200'][0] <= 0:
             exclusion_reason.append("slope_200 not > 0")
         if df['UDRatio'][0] <= 0.75:
             exclusion_reason.append("UDRatio not > 0.75")
         if not (df['150'][0] > df['200'][0]):
             exclusion_reason.append("150 not > 200")
         if not (df['50'][0] > df['150'][0]):
             exclusion_reason.append("50 not > 150")
         if not (df['50'][0] > df['200'][0]):
             exclusion_reason.append("50 not > 200")
         if df['$VolumeM'][0] < 25:
             exclusion_reason.append("$VolumeM not > 25")
         excluded_tickers.append((xyz, ', '.join(exclusion_reason)))
         print(f"not downloading: {xyz} - Reasons: {', '.join(exclusion_reason)}")

         # Log excluded tickers and their reasons
    if excluded_tickers:
        for ticker, reason in excluded_tickers:
            print(f"Excluded {ticker} for reasons: {reason}")

    # List of ticker symbols to remove
    excluded_symbols = [ticker for ticker, _ in excluded_tickers]

    # Filter out rows in screenerdf where the Symbol is in excluded_symbols
    screenerdf = screenerdf[~screenerdf['Symbol'].isin(excluded_symbols)]

    # Get the current date
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")

    # Create the file name with the date identifier
    file_name = f"filtered_sorted_screener_{current_date}.xlsx"

    # Export the DataFrame to Excel
    screenerdf.to_excel(file_name)

    os.startfile(fileName)
def etfHoldings(etf):
    #exchange = findExchange(ticker)
    header = {
        "User-Agent": "Mozilla/50 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.119 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    url = 'https://stockanalysis.com/etf/'+etf+'/holdings/'
    response = requests.get(url, headers=header)

    df = pd.read_html(response.text)
    df = df[0].dropna(subset=['Symbol'])
    #print(df['Symbol'].tolist())
    return df['Symbol'].tolist()


def create_vertical_table(doc, sector, industry, sub_industry, beta, std_dev, forward_eps_growth,
                           eps_growth_last_qtr, eps_growth_ttm_vs_prior_ttm, eps_growth_3_year, eps_growth_5_year,
                           eps_growth_proj_next_yr_vs_this_yr, revenue_growth_ttm_vs_prior_ttm, revenue_growth_last_qtr,
                           equity_summary_score,adrp):
    # Create a vertical table
    table = doc.add_table(rows=14, cols=2)
    table.autofit = True

    # Define the data to be added to the table
    table_data = [
        #('Ticker:', f'{ticker}'),
        ('Sector:', f'{sector}'),
        ('Industry:', f'{industry}'),
        ('Sub-Industry:', f'{sub_industry}'),
        ('Forward EPS Long Term Growth:', f'{forward_eps_growth}'),
        ('EPS Growth Last Qtr:', f'{eps_growth_last_qtr}'),
        ('EPS Growth TTM vs Prior TTM:', f'{eps_growth_ttm_vs_prior_ttm}'),
        ('EPS Growth 3 Year:', f'{eps_growth_3_year}'),
        ('EPS Growth 5 Year:', f'{eps_growth_5_year}'),
        ('EPS Growth Proj Next Yr vs This Yr:', f'{eps_growth_proj_next_yr_vs_this_yr}'),
        ('Revenue Growth TTM vs Prior TTM:', f'{revenue_growth_ttm_vs_prior_ttm}'),
        ('Revenue Growth Last Qtr:', f'{revenue_growth_last_qtr}'),
        ('Equity Summary Score:', f'{equity_summary_score}'),
        ('ADRP:', f'{adrp}')
    ]

    # Add data to the table using a loop
    for row_idx, (label, value) in enumerate(table_data):
        table.cell(row_idx, 0).text = label
        table.cell(row_idx, 1).text = value

    return table