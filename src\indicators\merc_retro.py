import datetime

# 2025 Mercury Retrograde Periods
d1_start = datetime.date(2025, 3, 15)
d1_end = datetime.date(2025, 4, 7)
d2_start = datetime.date(2025, 7, 18)
d2_end = datetime.date(2025, 8, 11)
d3_start = datetime.date(2025, 11, 9)
d3_end = datetime.date(2025, 11, 29)

curr = datetime.date.today()

def mercRetroWeight():
    now = curr
    if d1_start <= now <= d1_end or d2_start <= now <= d2_end or d3_start <= now <= d3_end:
        return 3
    elif (d1_start - datetime.timedelta(days=10) <= now <= d1_end + datetime.timedelta(days=10) or
          d2_start - datetime.timedelta(days=10) <= now <= d2_end + datetime.timedelta(days=10) or
          d3_start - datetime.timedelta(days=10) <= now <= d3_end + datetime.timedelta(days=10)):
        return 2
    else:
        return 1

# Uncomment to test the function
# print(mercRetroWeight())
