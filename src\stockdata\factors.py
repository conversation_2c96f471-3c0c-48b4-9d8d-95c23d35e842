import asyncio
import aiohttp
from util.constants import *
from numpy.core.fromnumeric import mean
import pandas as pd
from util.calculate_sd import calSDVix, calSDBondRatio, equityCBOEPCESd, squeezemetricsSd
from stockdata.data_source import getStockDataV3, getBasicStockData
from indicators.merc_retro import mercRetroWeight
from tabulate import tabulate

from util.scrapper import squeezeMetrics, CNNFearGreed


async def getRatio(session: aiohttp.ClientSession, s1,s2):
    df1 = await getBasicStockData(session, s1)
    df2 = await getBasicStockData(session, s2)
    df = pd.DataFrame()
    df['Close'] = df1['Close'] / df2['Close']
    return df


# derivatives deviations , example PCC PCCE, COT positioning, VIX positioning, gamma holding markets
# scrap from cboe
# currency trends
# inflation trends
# bond auctions bid/spread readings
# breadth indicators mcclean oscillator NYMO NAMO
# sentiment indicators
# themes driving market
# sector correlation
# CTA / hedge fund/ insti positions
# Money supply
# Fundamentals Valuation data


def map_cnn_fear_greed_to_category(score: int) -> int:
    if 0 <= score <= 25:
        return 4  # Extreme Fear
    elif 26 <= score <= 50:
        return 3  # Fear
    elif 51 <= score <= 75:
        return 2  # Greed
    elif 76 <= score <= 100:
        return 1  # Extreme Greed
    else:
        return 4  # Default to extreme fear if out of expected range

async def calculateFactors(session: aiohttp.ClientSession):

    cnn_fear_greed_raw = await CNNFearGreed(session)
    CNNFearGreed_processed = PCECorrelation[map_cnn_fear_greed_to_category(cnn_fear_greed_raw)]

    valArr = [CNNFearGreed_processed]

    PCE = PCECorrelation[equityCBOEPCESd()]

    #add skew

    await getStockDataV3(session, '^SKEW')

    valArr.append(PCE)

    # squeeze data
    squeeze_metrics_df = await squeezeMetrics(session)
    DIXGEX = squeezemetricsSd(squeeze_metrics_df)
    DIX = DIXGEXCorrelation[DIXGEX[0]]
    GEX = DIXGEXCorrelation[DIXGEX[1]]
    valArr.append(DIX)
    valArr.append(GEX)
    # calc VIX
    SDVIX = VIXCorrelation[calSDVix(await getStockDataV3(session, '^VIX'))]

    valArr.append(SDVIX)

    #yahoo df calulate

    SDSKEW = VIXCorrelation[calSDVix(await getStockDataV3(session, '^SKEW'))]

    valArr.append(SDSKEW)


    # merc retrograde
    MERCRETRO = VIXCorrelation[mercRetroWeight()]

    valArr.append(MERCRETRO)

    #bonds


    print("consolidated sentiment")
    print(tabulate(pd.DataFrame({
          "CNNFearGreed": cnn_fear_greed_raw,
          "PCE": PCE,
          "DIX": DIX,"GEX":GEX,
          "SDVIX": SDVIX,
          "MERCRETRO": MERCRETRO ,
          "SDSKEW":SDSKEW,
          #correct the below
          "IEI/HYG": VIXCorrelation[calSDBondRatio(await getRatio(session, 'IEI','HYG'),20)],
          "SPY/TLT": VIXCorrelation[calSDBondRatio(await getRatio(session, 'SPY','TLT'),20)],
          "QQQ/SPY": VIXCorrelation[calSDBondRatio(await getRatio(session, 'QQQ','SPY'),20)],
          "COPPER/GOLD": VIXCorrelation[calSDBondRatio(await getRatio(session, 'HG=F','GC=F'),20)],
          } ,

           index=[0]),headers='keys', tablefmt='fancy_grid'))
    #valArr = remove_all_occurrences(valArr,1)   # removing 1 as it is a non factor



    return mean(valArr)