# Bond Analysis Tools

This package provides tools for analyzing fixed income securities (bonds) with a focus on liquidity and bid-ask spread.

## Files

- `analysis.py` - Basic bond analysis script (original)
- `enhanced_analysis.py` - Advanced bond analysis with liquidity metrics
- `visualization.py` - Creates visualizations of bond data
- `run_analysis.py` - Main script to run both analysis and visualization

## Features

### Enhanced Analysis

The enhanced analysis script (`enhanced_analysis.py`) provides:

- **Liquidity Analysis**: Evaluates bonds based on bid-ask spread and quantity available
- **Yield Metrics**: Calculates various yield metrics including current yield, yield to worst, and total return
- **Credit Risk Assessment**: Converts credit ratings to numeric scores for comparison
- **Comprehensive Filtering**: Filters bonds based on multiple criteria
- **Excel Output**: Creates formatted Excel output with summary statistics

### Visualization

The visualization script (`visualization.py`) creates:

- Scatter plots of yield vs spread with liquidity score as color
- Scatter plots of yield vs maturity with rating as color
- Histograms of liquidity scores
- Bar charts of average yield by rating
- Correlation heatmaps between key metrics
- Charts of top bonds by liquidity and yield
- HTML report with all visualizations and summary statistics

## Usage

### Basic Usage

Run the complete analysis and visualization:

```
python run_analysis.py
```

### Advanced Usage

Run with custom parameters:

```
python run_analysis.py --file "path/to/your/bond_data.csv" --max-spread 1.5 --min-quantity 20 --min-yield 3.0 --min-rating 6
```

Run only the analysis:

```
python run_analysis.py --analysis-only
```

Run only the visualization on the most recent analysis:

```
python run_analysis.py --viz-only
```

## Parameters

- `--file`: Path to the bond CSV file (default is the hardcoded path in the script)
- `--max-spread`: Maximum acceptable bid-ask spread (default: 2.0)
- `--min-quantity`: Minimum quantity available (default: 10)
- `--min-ask-quantity`: Minimum required purchase quantity - the number in parentheses (default: 0)
- `--max-ask-quantity`: Maximum required purchase quantity - the number in parentheses (default: 1000000)
- `--min-yield`: Minimum yield to worst (default: 2.5)
- `--min-rating`: Minimum rating score on a scale of 0-10 (default: 5)
- `--callable`: Filter for callable bonds: 'all', 'callable', or 'non-callable' (default: 'all')
- `--call-protection`: Filter for bonds with call protection: 'all', 'protected', or 'unprotected' (default: 'all')
- `--tax-state`: State code for tax calculations (default: 'MA')
- `--federal-tax-rate`: Federal tax rate (default: 0.37 or 37%)
- `--state-tax-rate`: State tax rate (default: 0.05 or 5% for MA)

## Required Data Format

The scripts expect a CSV file with the following columns:

- `Cusip`: Bond CUSIP identifier
- `Description`: Bond description
- `Coupon`: Coupon rate
- `Maturity Date`: Maturity date
- `Price Bid`: Bid price
- `Price Ask`: Ask price
- `Ask Yield to Worst`: Yield to worst
- `Ask Yield to Maturity`: Yield to maturity
- `Quantity Ask(min)`: Minimum quantity available
- `Moody's Rating`: Moody's credit rating
- `S&P Rating`: S&P credit rating
- `State`: State (for municipal bonds)
- `Attributes`: Bond attributes

## Output

The scripts generate:

1. A CSV file with filtered bonds
2. An Excel file with the same data plus a summary sheet
3. A directory with visualizations and an HTML report

## Dependencies

- pandas
- numpy
- matplotlib
- seaborn
- openpyxl (for Excel output)

## Example

To find highly liquid investment-grade bonds with yields above 3%:

```
python run_analysis.py --max-spread 1.0 --min-quantity 25 --min-yield 3.0 --min-rating 5
```

To find bonds with reasonable minimum purchase requirements (10 bonds or less):

```
python run_analysis.py --max-ask-quantity 10 --min-yield 4.0
```

To find non-callable bonds with reasonable minimum purchase requirements:

```
python run_analysis.py --max-ask-quantity 10 --min-yield 4.0 --callable non-callable
```

To find bonds with call protection:

```
python run_analysis.py --call-protection protected --min-yield 4.0
```

To analyze bonds with after-tax returns for a California investor (9.3% state tax rate):

```
python run_analysis.py --max-ask-quantity 10 --min-yield 4.0 --tax-state CA --state-tax-rate 0.093
```
