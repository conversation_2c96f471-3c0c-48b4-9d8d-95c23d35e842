# low_volume_consolidation_scanner.py - Identifies stocks with very low relative volume for consolidation analysis

import requests
import json
import pandas as pd
from typing import List, Dict, Tuple
from datetime import datetime
import os
from dotenv import load_dotenv

# Import existing functions
from fidelity.common.api_headers import get_fidelity_headers_v2
from fidelity.scanner.volume_scanner import get_csv_stock_list_from_excel, chunk_list

load_dotenv()

def analyze_low_volume_stocks(response_data: Dict, max_relative_volume: float = 0.5) -> List[Tuple]:
    """
    Analyze stocks for very low relative volume indicating consolidation patterns.
    
    Args:
        response_data: API response containing quote data
        max_relative_volume: Maximum relative volume ratio (default 0.5 = 50% of average)
    
    Returns:
        List of tuples (symbol, relative_volume_ratio, price, pct_change, volume, avg_volume)
    """
    consolidation_candidates = []
    
    for data in response_data['quoteData']:
        try:
            symbol = data['symbol']
            volume = float(data['volume'].replace(',', ''))
            avgVol10Day = float(data['avgVol10Day'].replace(',', ''))
            lastPrice = data.get('lastPrice', 'N/A')
            pctChgToday = data.get('pctChgToday', 'N/A')
            
            # Skip if no volume data available
            if avgVol10Day == 0:
                continue
                
            relative_volume = volume / avgVol10Day
            
            # Filter for stocks with low relative volume (consolidation candidates)
            if relative_volume <= max_relative_volume:
                consolidation_candidates.append((
                    symbol,
                    relative_volume,
                    lastPrice,
                    pctChgToday,
                    volume,
                    avgVol10Day
                ))
                
        except (ValueError, KeyError) as e:
            print(f"Error processing data for symbol: {data.get('symbol', 'Unknown')}, Error: {e}")
            continue
    
    # Sort by relative volume (lowest first - most consolidated)
    consolidation_candidates.sort(key=lambda x: x[1])
    
    return consolidation_candidates

def scan_for_consolidation_stocks(file_path: str = None, max_relative_volume: float = 0.4) -> List[Tuple]:
    """
    Main function to scan for stocks with very low relative volume.
    
    Args:
        file_path: Path to Excel file with stock symbols (uses env var if None)
        max_relative_volume: Maximum relative volume ratio (default 0.4 = 40% of average)
    
    Returns:
        List of consolidation candidate stocks
    """
    if file_path is None:
        file_path = os.getenv('FIDELITY_FILE')
    
    if not file_path:
        raise ValueError("No file path provided and FIDELITY_FILE environment variable not set")
    
    print(f"🔍 Scanning for consolidation stocks from: {file_path}")
    print(f"📊 Max relative volume threshold: {max_relative_volume} ({max_relative_volume*100:.0f}% of 10-day average)")
    print(f"⏰ Scan time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 70)
    
    # Get stock list from Excel
    symbol_list = get_csv_stock_list_from_excel(file_path)
    print(f"📈 Total symbols to analyze: {len(symbol_list)}")
    
    # Process in chunks
    max_symbols_per_request = 50
    symbol_chunks = chunk_list(symbol_list, max_symbols_per_request)
    
    all_consolidation_stocks = []
    
    for i, symbols in enumerate(symbol_chunks, 1):
        print(f"Processing chunk {i}/{len(symbol_chunks)} ({len(symbols)} symbols)...", end=" ")
        
        symbols_string = ','.join(symbols)
        url = "https://digital.fidelity.com/prgw/digital/research/api/quote"
        
        payload = json.dumps({
            "symbol": symbols_string
        })
        
        headers = get_fidelity_headers_v2()
        
        try:
            response = requests.post(url, headers=headers, data=payload)
            response_data = response.json()
            
            # Analyze for low volume stocks
            consolidation_stocks = analyze_low_volume_stocks(response_data, max_relative_volume)
            all_consolidation_stocks.extend(consolidation_stocks)
            
            print(f"✅ Found {len(consolidation_stocks)} candidates")
            
        except (ValueError, KeyError) as e:
            print(f"❌ Failed: {e}")
            continue
    
    # Sort all results by relative volume (lowest first)
    all_consolidation_stocks.sort(key=lambda x: x[1])
    
    print(f"\n🎯 Found {len(all_consolidation_stocks)} total consolidation candidates")
    
    return all_consolidation_stocks

def display_consolidation_results(consolidation_stocks: List[Tuple], top_n: int = 20):
    """Display the consolidation analysis results in a formatted table."""
    
    if not consolidation_stocks:
        print("❌ No consolidation candidates found with the specified criteria.")
        return
    
    print(f"\n🏆 Top {min(top_n, len(consolidation_stocks))} Consolidation Candidates (Lowest Relative Volume):")
    print("=" * 90)
    print(f"{'Symbol':<8} {'Rel Vol':<8} {'Price':<10} {'% Change':<10} {'Volume':<12} {'Avg Vol':<12}")
    print("-" * 90)
    
    for i, (symbol, rel_vol, price, pct_change, volume, avg_volume) in enumerate(consolidation_stocks[:top_n], 1):
        # Format volume numbers for readability
        volume_str = f"{volume:,.0f}" if isinstance(volume, (int, float)) else str(volume)
        avg_volume_str = f"{avg_volume:,.0f}" if isinstance(avg_volume, (int, float)) else str(avg_volume)
        
        print(f"{symbol:<8} {rel_vol:<8.3f} {price:<10} {pct_change:<10} {volume_str:<12} {avg_volume_str:<12}")
    
    # Summary statistics
    if len(consolidation_stocks) > 0:
        avg_rel_vol = sum(stock[1] for stock in consolidation_stocks) / len(consolidation_stocks)
        min_rel_vol = min(stock[1] for stock in consolidation_stocks)
        max_rel_vol = max(stock[1] for stock in consolidation_stocks)
        
        print("\n📊 Summary Statistics:")
        print(f"   • Average relative volume: {avg_rel_vol:.3f}")
        print(f"   • Minimum relative volume: {min_rel_vol:.3f}")
        print(f"   • Maximum relative volume: {max_rel_vol:.3f}")

def export_to_csv(consolidation_stocks: List[Tuple], filename: str = None):
    """Export consolidation results to CSV file."""
    
    if not consolidation_stocks:
        print("No data to export.")
        return
    
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"consolidation_candidates_{timestamp}.csv"
    
    # Create DataFrame
    df = pd.DataFrame(consolidation_stocks, columns=[
        'Symbol', 'Relative_Volume', 'Price', 'Percent_Change', 'Volume', 'Average_Volume'
    ])
    
    # Save to CSV
    output_path = os.path.join("src", "data", "screener", filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    df.to_csv(output_path, index=False)
    
    print(f"💾 Results exported to: {output_path}")

if __name__ == "__main__":
    # Configuration
    MAX_RELATIVE_VOLUME = 0.4  # 40% of average volume
    TOP_N_RESULTS = 25
    EXPORT_TO_CSV = True
    
    try:
        # Run the consolidation scan
        consolidation_candidates = scan_for_consolidation_stocks(max_relative_volume=MAX_RELATIVE_VOLUME)
        
        # Display results
        display_consolidation_results(consolidation_candidates, top_n=TOP_N_RESULTS)
        
        # Export to CSV if requested
        if EXPORT_TO_CSV and consolidation_candidates:
            export_to_csv(consolidation_candidates)
        
        # Print symbols for easy copy-paste
        if consolidation_candidates:
            top_symbols = [stock[0] for stock in consolidation_candidates[:TOP_N_RESULTS]]
            print(f"\n📋 Top {len(top_symbols)} symbols for copy-paste:")
            symbol_chunks = [",".join(top_symbols[i:i+20]) for i in range(0, len(top_symbols), 20)]
            for chunk in symbol_chunks:
                print(chunk)
        
    except Exception as e:
        print(f"❌ Error during scan: {e}")
