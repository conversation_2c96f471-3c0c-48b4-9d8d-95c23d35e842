import os
import requests

# Repository details
repo_owner = "Fred6725"
repo_name = "rs-log"
directory_path = "output"  # Directory containing the CSV files
download_path = "./src/data/rs_data"  # Directory where CSV files will be saved

# Create the download directory if it does not exist
if not os.path.exists(download_path):
    os.makedirs(download_path)

# GitHub API endpoint to list contents of a directory
api_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{directory_path}"

def download_rs_files():
    try:
        # Get the contents of the directory
        response = requests.get(api_url)
        response.raise_for_status()
        files = response.json()

        # Filter and download each CSV file
        for file_info in files:
            if file_info["name"].endswith(".csv"):
                download_file(file_info["download_url"], file_info["name"])

        print("Download complete.")

    except requests.exceptions.RequestException as e:
        print(f"Error fetching file list: {e}")

def download_file(url, filename):
    try:
        response = requests.get(url)
        response.raise_for_status()

        file_path = os.path.join(download_path, filename)
        with open(file_path, "wb") as file:
            file.write(response.content)

        print(f"Downloaded: {filename}")

    except requests.exceptions.RequestException as e:
        print(f"Error downloading {filename}: {e}")

# Run the download process
#download_rs_files()
