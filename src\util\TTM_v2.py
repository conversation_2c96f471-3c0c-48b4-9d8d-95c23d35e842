import yfinance as yf
import pandas as pd
import pandas_ta as ta
from datetime import datetime, timedelta
from common.http_session import get_session
# Disable SSL verification to fix certificate issues
session = get_session('src/certs/my_ca_bundle.pem')


def calculate_squeeze_momentum(symbol, start_date=None, end_date=None, bb_length=20, bb_mult=2.0, kc_length=20, kc_mult=1.5):
    """
    Calculate the Squeeze Momentum indicator using pandas_ta.
    Returns a DataFrame with squeeze momentum values.
    """
    # Get data from Yahoo Finance with specific date range
    data = yf.download(symbol, start=start_date, end=end_date,session=session)

    # Ensure the index is datetime
    data.index = pd.to_datetime(data.index)

    # use_tr = kwargs.setdefault("tr", True)
    # asint = kwargs.pop("asint", True)
    # detailed = kwargs.pop("detailed", False)
    # lazybear = kwargs.pop("lazybear", False)



    args = {
    'lazybear': True,
    'use_tr': True
}

    squeeze_momentum = ta.squeeze(
        close=data['Close'],
        high=data['High'],
        low=data['Low'],
        bb_length=bb_length,
        bb_mult=bb_mult,
        kc_length=kc_length,
        kc_mult=kc_mult,
        bb_std=1.5,
        **args
    )



    return squeeze_momentum


# Set specific date range
start_date = '2024-01-15'  # Starting earlier to ensure enough data for moving averages
end_date = '2025-01-17'    # Including one day after to ensure we capture the 16th

# Run calculation
symbol = "DOCU"
print(calculate_squeeze_momentum(symbol, start_date, end_date).tail(20))
