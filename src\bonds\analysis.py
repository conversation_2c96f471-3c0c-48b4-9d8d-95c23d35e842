import pandas as pd

# Load the CSV file (replace with your file path)
file_path = r"C:\Users\<USER>\Downloads\Fidelity_FixedIncome_SearchResults (5).csv"
df = pd.read_csv(file_path)

# Clean CUSIP formatting
df['Cusip'] = df['Cusip'].str.extract(r'="(\w+)"')

# Convert necessary columns to numeric
df['Price Bid'] = pd.to_numeric(df['Price Bid'], errors='coerce')
df['Price Ask'] = pd.to_numeric(df['Price Ask'], errors='coerce')
df['Coupon'] = pd.to_numeric(df['Coupon'], errors='coerce')

# Drop rows with missing critical info
df = df.dropna(subset=['Price Bid', 'Price Ask', 'Coupon'])

# Calculate Spread
df['Spread'] = df['Price Ask'] - df['Price Bid']

# Filter for tight spreads
df = df[df['Spread'] <= 2.00]

# Set assumed face value
face_value = 1000

# Calculate financial metrics
df['Price Paid ($)'] = (df['Price Ask'] / 100) * face_value
df['Annual Coupon Income ($)'] = (df['Coupon'] / 100) * face_value
df['Current Yield (%)'] = (df['Annual Coupon Income ($)'] / df['Price Paid ($)']) * 100

# (Optional) Gain/loss to par at maturity
df['Gain/Loss to Par ($)'] = face_value - df['Price Paid ($)']
df['Total Return at Maturity ($)'] = df['Gain/Loss to Par ($)'] + df['Annual Coupon Income ($)']  # for 1 year
# You could divide gain/loss over remaining years if desired

# Sort by tightest spread
df = df.sort_values(by='Spread')

# Select final columns to export
columns_to_export = [
    'Cusip', 'Description', 'Coupon', 'Maturity Date',
    'Price Bid', 'Price Ask', 'Spread',
    'Price Paid ($)', 'Annual Coupon Income ($)', 'Current Yield (%)',
    'Gain/Loss to Par ($)', 'Total Return at Maturity ($)',
    "Moody's Rating", "S&P Rating", 'Ask Yield to Worst'
]

output = df[columns_to_export]

# Export final CSV
output_path = "tightest_spread_bonds.csv"
output.to_csv(output_path, index=False)

print(f"Analysis complete. Exported to {output_path}")
