from ruamel.yaml import YAML
import os

# Use relative path for config file
CONFIG_FILE = os.path.join(os.path.dirname(__file__), 'config.yaml')
yaml = YAML()
yaml.preserve_quotes = True # Optional: Keeps quotes in YAML if they exist

def load_config():
    """Loads configuration from config.yaml."""
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = yaml.load(f)
        # Provide defaults for potentially missing keys
        config.setdefault('portfolio_capital', 100000)
        config.setdefault('run_mode', 'loop')
        config.setdefault('default_ticker', '')
        config.setdefault('remember_last_ticker', True)
        config.setdefault('default_entry_price_method', 'last_close')
        config.setdefault('default_stop_loss_type', 'atr2')
        config.setdefault('default_stop_loss_value', 0.0)
        config.setdefault('default_show_extras', True)
        return config
    except FileNotFoundError:
        print(f"Warning: {CONFIG_FILE} not found. Using default settings.")
        # Return a default dictionary if file not found
        return {
            'portfolio_capital': 100000, 'run_mode': 'loop', 'default_ticker': '',
            'remember_last_ticker': True, 'default_entry_price_method': 'last_close',
            'default_stop_loss_type': 'atr2', 'default_stop_loss_value': 0.0,
            'default_show_extras': True
        }
    except Exception as e:
        print(f"Error loading {CONFIG_FILE}: {e}. Using default settings.")
        # Return defaults on other errors too
        return {
            'portfolio_capital': 100000, 'run_mode': 'loop', 'default_ticker': '',
            'remember_last_ticker': True, 'default_entry_price_method': 'last_close',
            'default_stop_loss_type': 'atr2', 'default_stop_loss_value': 0.0,
            'default_show_extras': True
        }

def save_config(config):
    """Saves configuration to config.yaml."""
    try:
        with open(CONFIG_FILE, 'w') as f:
            yaml.dump(config, f)
    except Exception as e:
        print(f"Error saving config to {CONFIG_FILE}: {e}")
