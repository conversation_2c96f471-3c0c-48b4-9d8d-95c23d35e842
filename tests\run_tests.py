"""
Main test runner script for PythonicFin.
"""

import sys
import os
import unittest

# Add src directory to Python path
sys.path.append('src')

# Import test modules
from test_getStockDataV3 import test_getStockDataV3
from test_ttm_volume import test_ttm_volume
from test_specific_stocks import test_pltr, test_msft, test_nvda
from test_yfinance_fixed import test_yfinance_basic
from test_yfinance_upgrade import run_all_tests as test_yfinance_upgrade
from test_df_structure import test_df_structure
from test_imports import test_imports
from test_optimized_calculations import run_tests as test_optimized_calculations
from test_real_stock_data import test_real_stock_data

def run_all_tests():
    """Run all tests."""
    print("Running all tests...")

    # Run imports test
    print("\n=== Testing Package Imports ===")
    try:
        import test_imports
        print("Imports test completed")
    except Exception as e:
        print(f"Imports test failed: {e}")

    # Run yfinance basic test
    print("\n=== Testing Basic yfinance Functionality ===")
    test_yfinance_basic()

    # Run yfinance upgrade test
    print("\n=== Testing yfinance 0.2.54 Compatibility ===")
    test_yfinance_upgrade()

    # Run DataFrame structure test
    print("\n=== Testing DataFrame Structure ===")
    try:
        test_df_structure()
    except Exception as e:
        print(f"DataFrame structure test failed: {e}")

    # Run getStockDataV3 test
    print("\n=== Testing getStockDataV3 ===")
    test_getStockDataV3()

    # Run TTM and volume test
    print("\n=== Testing TTM and Volume ===")
    test_ttm_volume()

    # Run optimized calculations test
    print("\n=== Testing Optimized Calculations ===")
    try:
        test_optimized_calculations()
        print("Optimized calculations test completed")
    except Exception as e:
        print(f"Optimized calculations test failed: {e}")

    # Run real stock data test
    print("\n=== Testing Real Stock Data ===")
    try:
        test_real_stock_data()
        print("Real stock data test completed")
    except Exception as e:
        print(f"Real stock data test failed: {e}")

    # Run PLTR test
    print("\n=== Testing PLTR ===")
    test_pltr()

    # Run MSFT test (optional)
    # print("\n=== Testing MSFT ===")
    # test_msft()

    # Run NVDA test (optional)
    # print("\n=== Testing NVDA ===")
    # test_nvda()

    print("\nAll tests completed!")

if __name__ == "__main__":
    run_all_tests()
