# chart_downloader.py - Downloads and processes chart data from Fidelity
#
# This module provides functions for downloading stock charts from Fidelity and
# processing them for analysis. It can download charts for multiple stocks and
# organize them into Word documents with additional stock information.
#
# Usage:
#   1. Import the module: from fidelity.chart_analysis.chart_downloader import downloadChartsFidelityV2
#   2. Call the function with a list of stock symbols: downloadChartsFidelityV2(['AAPL', 'MSFT'], 'output_dir')
#
# The module handles:
#   - Downloading chart images from Fidelity
#   - Creating Word documents with charts and stock information
#   - Organizing charts by sector and industry

import os
import time
import requests
import docx
import datetime
import asyncio
import aiohttp
from stockdata.data_source import getStockDataV3
from common.http_session import get_session

headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36'}

def download_image(url, file_path):
    """
    Download an image from a URL and save it to a file.

    Args:
        url (str): URL of the image to download
        file_path (str): Path where the image will be saved

    Returns:
        bool: True if download was successful, False otherwise
    """
    try:
        # First try with the default session (which might be curl_cffi)
        try:
            session = get_session()

            # For curl_cffi, we need to use impersonate="chrome" and stream=True
            # This will be handled by get_session() for curl_cffi sessions
            response = session.get(url, stream=True)

        except Exception as curl_error:
            # If we get a "stream mode is not enabled" error or any other error,
            # fall back to standard requests
            if "stream mode is not enabled" in str(curl_error) or True:
                print(f"Falling back to standard requests due to: {curl_error}")
                session = get_session(use_standard_requests=True)
                response = session.get(url, stream=True)

        # Check if the request was successful
        if not response.ok:
            print(f"Request failed with status code: {response.status_code}")
            return False

        # Save the image data to file
        with open(file_path, 'wb') as handle:
            # Download and write the image data in chunks
            for block in response.iter_content(1024):
                if not block:
                    break
                handle.write(block)

        return True
    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return False

def add_image_to_doc(doc, file_path,clean_image=False):
    """
    Add an image to a Word document and then remove the image file.

    Args:
        doc (docx.Document): Word document object to add the image to
        file_path (str): Path to the image file

    Note:
        The image file is removed after being added to the document
    """
    try:
        # Add the image to the document
        doc.add_picture(file_path)
    except Exception as e:
        print(f"Error adding image {file_path} to document: {e}")
    finally:
        # Clean up by removing the temporary image file
        if clean_image:
            os.remove(file_path)

def create_vertical_table(doc, sector, industry, sub_industry, forward_eps_growth,
                           eps_growth_last_qtr, eps_growth_ttm_vs_prior_ttm, eps_growth_3_year, eps_growth_5_year,
                           eps_growth_proj_next_yr_vs_this_yr, revenue_growth_ttm_vs_prior_ttm, revenue_growth_last_qtr,
                           equity_summary_score):
    # Create a vertical table
    table = doc.add_table(rows=14, cols=2)
    table.autofit = True

    # Define the data to be added to the table
    table_data = [
        #('Ticker:', f'{ticker}'),
        ('Sector:', f'{sector}'),
        ('Industry:', f'{industry}'),
        ('Sub-Industry:', f'{sub_industry}'),
        ('Forward EPS Long Term Growth:', f'{forward_eps_growth}'),
        ('EPS Growth Last Qtr:', f'{eps_growth_last_qtr}'),
        ('EPS Growth TTM vs Prior TTM:', f'{eps_growth_ttm_vs_prior_ttm}'),
        ('EPS Growth 3 Year:', f'{eps_growth_3_year}'),
        ('EPS Growth 5 Year:', f'{eps_growth_5_year}'),
        ('EPS Growth Proj Next Yr vs This Yr:', f'{eps_growth_proj_next_yr_vs_this_yr}'),
        ('Revenue Growth TTM vs Prior TTM:', f'{revenue_growth_ttm_vs_prior_ttm}'),
        ('Revenue Growth Last Qtr:', f'{revenue_growth_last_qtr}'),
        ('Equity Summary Score:', f'{equity_summary_score}')
    ]

    # Add data to the table using a loop
    for row_idx, (label, value) in enumerate(table_data):
        table.cell(row_idx, 0).text = label
        table.cell(row_idx, 1).text = value

    return table

async def process_ticker(session: aiohttp.ClientSession, row, doc, save_doc,daily_id, wkly_id):
    xyz = row['Symbol']
    df = await getStockDataV3(session, xyz)

    if (df is not None
        and df.size > 0
        and df['slope_50'][0] > 0
        and df['slope_200'][0] > 0
        and df['UDRatio'][0] > 0.75
        and df['150'][0] > df['200'][0]
        and df['50'][0] > df['150'][0]
        and df['50'][0] > df['200'][0]
        and df['$VolumeM'][0] >= 25
        and ('RS_Rating' in df.columns and df['RS_Rating'][0] > 77)
    ):
        if save_doc:

         base_url_daily = f'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i={daily_id}&r=tdy'
         base_url_weekly = f'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=5&mn=0&dy=0&i={wkly_id}&r=tdy'

         daily_url = base_url_daily.replace('tdy', str(time.time())).replace('ticker', xyz)
         daily_image_path = f'src/data/images{xyz}.png'

         if download_image(daily_url, daily_image_path):
             add_image_to_doc(doc, daily_image_path)

         weekly_url = base_url_weekly.replace('tdy', str(time.time())).replace('ticker', xyz)
         weekly_image_path = f'src/data/images{xyz}_week.png'

         if download_image(weekly_url, weekly_image_path):
             add_image_to_doc(doc, weekly_image_path)


             create_vertical_table(
doc,
row['Sector'],
row['Industry'],
row['Sub-Industry'],
#row['Beta (1 Year Annualized)'],
#row['Standard Deviation (1 Yr Annualized)'],
row['Forward EPS Long Term Growth (3-5 Yrs)'],
row['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'],
row['EPS Growth (TTM vs Prior TTM)'],
row['EPS Growth (3 Year History)'],
row['EPS Growth (5 Year Historical)'],
row['EPS Growth (Proj Next Yr vs. This Yr)'],
row['Revenue Growth (TTM vs. Prior TTM)'],
row['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'],
row['Equity Summary Score (ESS) from LSEG StarMine'],
round(df['ADRP'][0], 2))

    else:
        return xyz, determine_exclusion_reasons(df) , df
    return None, None , df

def determine_exclusion_reasons(df):
    reasons = []
    if df is None:
       reasons.append("DataFrame is None")
       return
    if df.size == 0:
        reasons.append("DataFrame is empty")
    if df['slope_50'].iloc[0] <= 0:
        reasons.append("slope_50 not > 0")
    if df['slope_200'].iloc[0] <= 0:
        reasons.append("slope_200 not > 0")
    if df['UDRatio'].iloc[0] <= 0.75:
        reasons.append("UDRatio not > 0.75")
    if not (df['150'].iloc[0] > df['200'].iloc[0]):
        reasons.append("150 not > 200")
    if not (df['50'].iloc[0] > df['150'].iloc[0]):
        reasons.append("50 not > 150")
    if not (df['50'].iloc[0] > df['200'].iloc[0]):
        reasons.append("50 not > 200")
    if df['$VolumeM'].iloc[0] < 25:
        reasons.append("$VolumeM not > 25")
    # Check if RS_Rating column exists before accessing it
    if 'RS_Rating' in df.columns and df['RS_Rating'].iloc[0] <= 77:
        reasons.append("RS_Rating not > 77")
    elif 'RS_Rating' not in df.columns:
        reasons.append("RS_Rating not available")
    return ', '.join(reasons)

def save_filtered_screener(screenerdf, excluded_tickers):
    excluded_symbols = [ticker for ticker, _ in excluded_tickers]
    df = screenerdf[~screenerdf['Symbol'].isin(excluded_symbols)] # # Create a filtered copy of the DataFrame to avoid modifying a slice    df = screenerdf.loc[~screenerdf['Symbol'].isin(excluded_symbols)].copy()

    # Calculate the size of each group
    df['Sector_Count'] = df.groupby('Sector')['Percentile'].transform('size')
    df['Industry_Count'] = df.groupby(['Sector', 'Industry'])['Percentile'].transform('size')
    df['SubIndustry_Count'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].transform('size')

    # Sort by 'Sector_Count', 'Industry_Count', 'SubIndustry_Count' in descending order
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], ascending=[False, False, False])

    # Rank based on 'Percentile' within each group defined by 'Sector', 'Industry', 'Sub-Industry'
    df['Rank'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].rank(ascending=False)

    # Final sort by the calculated 'Rank'
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count', 'Rank'], ascending=[False, False, False, True])

    # Drop auxiliary columns if not needed
    df.drop(columns=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], inplace=True)

    import os

    # Create data directory if it doesn't exist
    screener_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'data', 'screener')
    os.makedirs(screener_dir, exist_ok=True)

    # Create file name with date
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    file_name = f"filtered_sorted_screener_{current_date}.xlsx"

    # Full path to the file
    file_path = os.path.join(screener_dir, file_name)

    # Save the file
    df.to_excel(file_path)

    print(f"Saved filtered screener to {file_path}")




async def filter_stocks(session: aiohttp.ClientSession, screenerdf):
    """
    Filter stocks based on technical criteria and return filtered dataframe and excluded tickers
    """
    excluded_tickers = []

    for _, row in screenerdf.iterrows():
        # Get the stock data
        xyz = row['Symbol']
        df = await getStockDataV3(session, xyz)

        # Check if stock meets technical criteria
        if (df is not None
            and df.size > 0
            and df['slope_50'].iloc[0] > 0
            and df['slope_200'].iloc[0] > 0
            and df['UDRatio'].iloc[0] > 0.75
            and df['150'].iloc[0] > df['200'].iloc[0]
            and df['50'].iloc[0] > df['150'].iloc[0]
            and df['50'].iloc[0] > df['200'].iloc[0]
            and df['$VolumeM'].iloc[0] >= 25
            and ('RS_Rating' in df.columns and df['RS_Rating'].iloc[0] > 77)
        ):
            # Update screenerdf with additional metrics if stock passes filters
            if not df.empty:
                if 'TTM' in df.columns:
                    try:
                        screenerdf.at[row.name, 'TTM'] = df['TTM'].iloc[0]
                    except (KeyError, IndexError):
                        print(f"Issue adding TTM for {xyz}")
                if 'RS_Rating' in df.columns:
                    try:
                        screenerdf.at[row.name, 'Percentile'] = df['RS_Rating'].iloc[0]
                    except (KeyError, IndexError):
                        print(f"Issue adding RS_Rating for {xyz}")
        else:
            # Add to excluded tickers if stock doesn't meet criteria
            reason = determine_exclusion_reasons(df)
            excluded_tickers.append((xyz, reason))

    return screenerdf, excluded_tickers

def  download_stock_charts(screenerdf, doc_name=None, daily_id=None, wkly_id=None,clean_image=False):
    """
    Download charts for filtered stocks and create document
    """
    if  doc_name is None or daily_id is None or wkly_id is None:
        print("Missing required parameters: doc_name, daily_id, or wkly_id")
        return

    doc = docx.Document()
    file_name = f'{doc_name}.docx'

    for _, row in screenerdf.iterrows():
        xyz = row['Symbol']

        # Download daily chart
        base_url_daily = f'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i={daily_id}&r=tdy'
        daily_url = base_url_daily.replace('tdy', str(time.time())).replace('ticker', xyz)
        daily_image_path = f'src/data/images{xyz}.png'

        if download_image(daily_url, daily_image_path):
            add_image_to_doc(doc, daily_image_path,clean_image)

        # Download weekly chart
        base_url_weekly = f'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=2&mn=6&dy=0&i={wkly_id}&r=tdy'
        weekly_url = base_url_weekly.replace('tdy', str(time.time())).replace('ticker', xyz)
        weekly_image_path = f'src/data/images{xyz}_week.png'

        if download_image(weekly_url, weekly_image_path):
            add_image_to_doc(doc, weekly_image_path,clean_image)

        # Add stock details table
        create_vertical_table(
            doc,
            row['Sector'],
            row['Industry'],
            row['Sub-Industry'],
            row['Forward EPS Long Term Growth (3-5 Yrs)'],
            row['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'],
            row['EPS Growth (TTM vs Prior TTM)'],
            row['EPS Growth (3 Year History)'],
            row['EPS Growth (5 Year Historical)'],
            row['EPS Growth (Proj Next Yr vs. This Yr)'],
            row['Revenue Growth (TTM vs. Prior TTM)'],
            row['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'],
            row['Equity Summary Score (ESS) from LSEG StarMine'],
            #round(getStockDataV3(xyz)['ADRP'].iloc[0], 2)
        )

        print("Processed chart for stock:",xyz)

    doc.save(file_name)
    os.startfile(file_name)

async def downloadChartsFidelityV2(screenerdf, doc_name=None):
    """
    Main function that coordinates filtering and chart download
    """
    async with aiohttp.ClientSession() as session:
        # Step 1: Filter stocks
        filtered_df, excluded_tickers = await filter_stocks(session, screenerdf)

    # Step 2: Print excluded tickers
    if excluded_tickers:
        for ticker, reason in excluded_tickers:
            print(f"Excluded {ticker} for reasons: {reason}")

    # Step 3: Save filtered screener
    save_filtered_screener(filtered_df, excluded_tickers)

    # Step 4: Download charts if document name is provided
    if doc_name:
        daily_id = input("Enter daily id: ")
        wkly_id = input("Enter weekly id: ")
        clean_image = input("Clean image? (Y/N): ").strip().lower() == 'y'
        filter_ttm = input("Filter for TTM squeeze? (Y/N): ").strip().lower() == 'y'

        # Apply TTM filter if requested
        if filter_ttm and 'TTM' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['TTM'] == True]
            print(f"Filtered to {len(filtered_df)} stocks with TTM squeeze")

        # Additional filters
        # min_rs = input("Enter minimum RS Rating (default 80): ")
        # if min_rs.strip() and min_rs.isdigit():
        #     min_rs = int(min_rs)
        #     filtered_df = filtered_df[filtered_df['Percentile'] >= min_rs]
        #     print(f"Filtered to {len(filtered_df)} stocks with RS Rating >= {min_rs}")

        # min_volume = input("Enter minimum volume in millions (default 25): ")
        # if min_volume.strip() and min_volume.replace('.', '', 1).isdigit():
        #     min_volume = float(min_volume)
        #     filtered_df = filtered_df[filtered_df['$Volume'] >= min_volume]
        #     print(f"Filtered to {len(filtered_df)} stocks with volume >= {min_volume}M")

        download_stock_charts(filtered_df, doc_name, daily_id, wkly_id, clean_image)
