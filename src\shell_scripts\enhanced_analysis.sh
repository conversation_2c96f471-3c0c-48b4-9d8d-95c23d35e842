#!/bin/bash

# Change to the script directory
cd "$(dirname "$0")/../.."

# Set environment variables from .env file
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Set PYTHONPATH to include the src directory
export PYTHONPATH="$(pwd)/src"

# Activate the Python virtual environment and run the Python module
source venv/bin/activate && python -m fidelity.chart_analysis.enhanced_analysis
