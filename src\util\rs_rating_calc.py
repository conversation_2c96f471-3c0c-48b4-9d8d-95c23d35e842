import yfinance as yf
import pandas as pd
from common.http_session import get_session

session = get_session('src/certs/my_ca_bundle.pem')

def get_environment_values():
    # Read the CSV file without headers using relative path
    import os
    rs_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'rs_data', 'RSRATING.csv')
    df = pd.read_csv(rs_data_path, header=None)

    # Extract the entire fifth column (index 4)
    desired_column = df.iloc[:, 4]
    # Sort and get unique values
    unique_values = sorted(set(desired_column), reverse=True)
    # Return the first 7 unique values (or less if there aren't 7)
    return unique_values[:7]

def f_attributePercentile(totalRsScore, tallerPerf, smallerPerf, rangeUp, rangeDn, weight):
    sum_val = totalRsScore + (totalRsScore - smallerPerf) * weight
    if sum_val > tallerPerf - 1:
        sum_val = tallerPerf - 1
    k1 = smallerPerf / rangeDn
    k2 = (tallerPerf - 1) / rangeUp
    k3 = (k1 - k2) / (tallerPerf - 1 - smallerPerf)
    RsRating = sum_val / (k1 - k3 * (totalRsScore - smallerPerf))
    if RsRating > rangeUp:
        RsRating = rangeUp
    if RsRating < rangeDn:
        RsRating = rangeDn
    return RsRating

def calculate_rs_rating(ticker, stock_data, sp500_data):
    periods = [63, 126, 189, 252]
    stock_perf = [stock_data['Close'].iloc[-1] / stock_data['Close'].iloc[-n] for n in periods]
    sp500_perf = [sp500_data['Close'].iloc[-1] / sp500_data['Close'].iloc[-n] for n in periods]

    # Calculate RS Score
    weights = [0.4, 0.2, 0.2, 0.2]
    rs_stock = sum(p * w for p, w in zip(stock_perf, weights))
    rs_ref = sum(p * w for p, w in zip(sp500_perf, weights))

    totalRsScore = (rs_stock / rs_ref) * 100

    # Simulating the environment (you may need to adjust these values)
    environment = get_environment_values()

    # Assign rating
    if totalRsScore >= environment[0]:
        return 99
    elif totalRsScore <= environment[-1]:
        return 1

    for i in range(len(environment) - 1):
        if environment[i+1] <= totalRsScore < environment[i]:
            ranges = [(98, 90), (89, 70), (69, 50), (49, 30), (29, 10), (9, 2)]
            weights = [0.33, 2.1, 0, 0, 0, 0]
            return f_attributePercentile(totalRsScore, environment[i], environment[i+1],
                                         ranges[i][0], ranges[i][1], weights[i])

    return -1  # Should not reach here

# Example usage
ticker = "IONQ"
spxdf = yf.download('^GSPC', period='2y', interval='1d',session=session)
tickerdf = yf.download(ticker, period='2y', interval='1d',session=session)

rs_rating = calculate_rs_rating(ticker, tickerdf, spxdf)
print(f"The RS Rating for {ticker} is: {rs_rating:.2f}")