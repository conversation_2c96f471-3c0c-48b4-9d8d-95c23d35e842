# Unit Tests for PythonicFin

This directory contains unit tests for the PythonicFin package.

## Running Tests

To run all tests, navigate to the project root directory and run:

```bash
python tests/run_tests.py
```

To run a specific test file:

```bash
python tests/test_getStockDataV3.py
```

## Test Files

### Core Functionality Tests
- `test_getStockDataV3.py`: Tests the `getStockDataV3` function to ensure it can retrieve stock data correctly.
- `test_ttm_volume.py`: Tests the TTM and volume calculations to ensure they're working correctly.
- `test_specific_stocks.py`: Tests the calculations for specific stocks like PLTR, MSFT, and NVDA.

### yfinance Compatibility Tests
- `test_yfinance_fixed.py`: Tests basic yfinance functionality to ensure our fixes are working.
- `test_yfinance_upgrade.py`: Comprehensive tests for yfinance 0.2.54 compatibility.

### Optimized Calculations Tests
- `test_optimized_calculations.py`: Tests the optimized TTM, extension, and volume calculations.
- `test_real_stock_data.py`: Tests the optimized calculations with real stock data.

### Utility Tests
- `test_df_structure.py`: Tests the DataFrame structure to understand how yfinance returns data.
- `test_imports.py`: Tests that all required packages are installed correctly.

### Test Runner
- `run_tests.py`: Main script to run all tests in sequence.

## Adding New Tests

When adding new tests, please follow these guidelines:

1. Create a new file with a descriptive name, prefixed with `test_`.
2. Include docstrings explaining what the test is checking.
3. Add the test to this README file.
4. Update the `run_tests.py` file to include your new test.
