"""
Test script to verify that yfinance is working correctly with our fixes.
"""

import yfinance as yf
import pandas as pd

def test_yfinance_basic():
    """Test basic yfinance functionality."""
    # Print yfinance version
    print(f"yfinance version: {yf.__version__}")
    
    # Test the basic download commands that are working
    print("\nTesting download of ^GSPC (S&P 500)...")
    try:
        spxdf = yf.download('^GSPC', period='1d', interval='1d')
        
        if spxdf is not None and not spxdf.empty:
            print(f"Success! Downloaded {len(spxdf)} rows of data")
            print("\nFirst 3 rows:")
            print(spxdf.head(3))
        else:
            print("Error: No data downloaded")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test with a specific ticker
    ticker = "AAPL"
    print(f"\nTesting download of {ticker}...")
    try:
        tickerdf = yf.download(ticker, period='1d', interval='1d')
        
        if tickerdf is not None and not tickerdf.empty:
            print(f"Success! Downloaded {len(tickerdf)} rows of data")
            print("\nFirst 3 rows:")
            print(tickerdf.head(3))
            return True
        else:
            print("Error: No data downloaded")
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_yfinance_basic()
