#https://pro-api.coinmarketcap.com/v3/fear-and-greed/historical?api_key=YOUR_API_KEY

#sample resp
#{"data":[{"timestamp":"1736208000","value":66,"value_classification":"Greed"},{"timestamp":"1736121600","value":60,"value_classification":"Greed"},...]}

#write a script to load key using dotenv & understand the resp to plot the crypto fear and greed index
import os
import matplotlib.pyplot as plt
from datetime import datetime
from dotenv import load_dotenv
from common.http_session import get_session

# Load environment variables
load_dotenv()

# Get API key from environment variables
API_KEY = os.getenv('CMC_PRO_API_KEY')
url = f"https://pro-api.coinmarketcap.com/v3/fear-and-greed/historical?CMC_PRO_API_KEY={API_KEY}"

def plot():
    # Create a session with proper SSL certificate handling
    session = get_session()

    # Make the request with our custom session
    response = session.get(url)

    # Check if the request was successful
    if not response.ok:
        print(f"Error: API request failed with status code {response.status_code}")
        print(f"Response: {response.text}")
        return

    # Parse the JSON response
    try:
        data = response.json()
    except Exception as e:
        print(f"Error parsing JSON response: {e}")
        print(f"Response text: {response.text[:200]}...")  # Print first 200 chars
        return

    # Check if the 'data' key exists in the response
    if 'data' not in data:
        print(f"Error: 'data' key not found in API response")
        print(f"Response keys: {list(data.keys())}")
        print(f"Response content: {data}")
        return

    # Extract useful data from the response
    timestamps = []
    values = []
    value_classifications = []

    try:
        for entry in data["data"]:
            timestamps.append(datetime.fromtimestamp(int(entry["timestamp"])))
            values.append(entry["value"])
            value_classifications.append(entry["value_classification"])
    except Exception as e:
        print(f"Error processing data entries: {e}")
        return

    # Plot the data if we have data to plot
    if timestamps and values:
        plt.figure(figsize=(10, 5))
        plt.plot(timestamps, values, marker='o', linestyle='-')
        plt.title("Crypto Fear and Greed Index")
        plt.xlabel("Date")
        plt.ylabel("Value")
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.grid(True)
        plt.show()
    else:
        print("No data available to plot")
