import numpy as np
import pandas as pd
import requests
import time
from itertools import cycle

from util.scrapper import scrapEarnings


from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Get API key from environment variables
key = os.environ.get('ALPHA_VANTAGE_API_KEY', 'demo')

# If you don't have an API key, get one from https://www.alphavantage.co/support/#api-key

# Use relative path for exchange files
nasdaq_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'exchange', 'nasdaqlisted.txt')
nyse_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'exchange', 'otherlisted.txt')

nasdaq = pd.read_csv(nasdaq_path, sep="|")
nyse = pd.read_csv(nyse_path, sep="|")


def getEarningsData(ticker):
    data = ''
    try:
        # replace the "demo" apikey below with your own key from https://www.alphavantage.co/support/#api-key
        url = 'https://www.alphavantage.co/query?function=EARNINGS&symbol='+ticker+'&apikey='+key
        #url = 'https://www.alphavantage.co/query?function=EARNINGS&symbol=IBM&apikey=demo'
        r = requests.get(url)
        data = r.json()
        qearnings = data["quarterlyEarnings"]
        df = pd.DataFrame(qearnings)
        df.replace("None", np.nan, inplace=True)
        df['reportedEPS'] = pd.to_numeric(df['reportedEPS'])
        df['fiscalDateEnding'] = pd.to_datetime(df['fiscalDateEnding'])
        df['fiscalDateEnding_m'] = df['fiscalDateEnding'].dt.strftime('%m/%d')
        yoy = df.groupby(['fiscalDateEnding_m'])[
            'reportedEPS'].pct_change(-1)*100*np.sign(df['reportedEPS'].shift(-1))
        #print(yoy.round(0))
        qoq = (df['reportedEPS'].pct_change(-1)*100 *
               np.sign(df['reportedEPS'].shift(-1)))
        #print(qoq.round(0))
        print(qoq.round(0).head(3).tolist())
        print(yoy.round(0).head(3).tolist())
    except Exception as ex:
        print(ex)
        return data


#getEarningsData('SNAP')

def getRevenueData(ticker):
    data = ''
    try:
        # replace the "demo" apikey below with your own key from https://www.alphavantage.co/support/#api-key
        url = 'https://www.alphavantage.co/query?function=INCOME_STATEMENT&symbol=' + \
            ticker+'&apikey='+key
        #url = 'https://www.alphavantage.co/query?function=EARNINGS&symbol=IBM&apikey=demo'
        r = requests.get(url)
        data = r.json()
        qearnings = data["quarterlyReports"]
        df = pd.DataFrame(qearnings)
        df.replace("None", np.nan, inplace=True)
        df['totalRevenue'] = pd.to_numeric(df['totalRevenue'])
        dates = (df['fiscalDateEnding'][:4].tolist())
        df['fiscalDateEnding'] = pd.to_datetime(df['fiscalDateEnding'])
        df['fiscalDateEnding_m'] = df['fiscalDateEnding'].dt.strftime('%m/%d')
        fruits = cycle(dates)
        df['Fruit_Type'] = [next(fruits) for fruit in range(len(df))]
        #print(df)
        yoy = df.groupby(['Fruit_Type'])[
            'totalRevenue'].pct_change(-1)*100*np.sign(df['totalRevenue'].shift(-1))
        qoq = (df['totalRevenue'].pct_change(-1)*100 *
               np.sign(df['totalRevenue'].shift(-1)))
        print(qoq.round(0).head(3).tolist())
        print(yoy.round(0).head(3).tolist())
    except Exception as ex:
        print(ex)
        return data
#getRevenueData('U')


def clean(df):
    # 'million':'000000','billion':'000000000'

    df = df.replace({'\$': '', '\(': '-', '\)': '', '\+': '', ',': ''},
                    regex=True).drop_duplicates()
    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
    df = df.dropna(subset=['Date'])
    df['Beat/Miss'] = df['Beat/Miss'].replace('-', 0)
    df['Actual Revenue'] = df['Actual Revenue'].replace(
        {'\$': '', '\(': '-', '\)': '', '\+': ''}, regex=True)
    df[['Actual Revenue', 'factor']
       ] = df['Actual Revenue'].str.split(' ', expand=True)
    df['factor'] = df['factor'].replace(
        {'million': '1000000', 'billion': '1000000000'}, regex=True)
    df['Actual Revenue'] = pd.to_numeric(df['Actual Revenue'])
    df['factor'] = pd.to_numeric(df['factor'])
    df['Reported EPS'] = pd.to_numeric(df['Reported EPS'])
    #df['Reported EPS'] = pd.to_numeric(df['Reported EPS'])
    df['Actual Revenue'] = df['Actual Revenue'] * df['factor']

    return df


def getFundamentals(ticker):
    #df = pd.DataFrame('./nasdaqlisted.txt')
    exchange = ''
    if ticker in nasdaq.values:
       exchange = 'NASDAQ'
    elif ticker in nyse.values:
        exchange = 'NYSE'
    else:
        exchange = 'NYSEAMERICAN'
    error = True
    df = pd.DataFrame()
    retry = 0
    while error:
        try:
         if(retry == 5):
             return [], []
         print(ticker, exchange)
         dfs = scrapEarnings(ticker, exchange)
         for val in dfs:
             print(val)
             print(val.columns.values)
             if 'Reported EPS' in val.columns.values:
                 df = val
                 break
         print(df)
         df = clean(df)
         error = False
        except Exception as ex:
            raise ex
            retry = retry + 1
            time.sleep(3)
            continue

     # Convert 'Date' to datetime format, ignoring errored rows
    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

    # Drop rows with NaT values in 'Date' column
    df = df.dropna(subset=['Date'])

    print(df)

    dates = (df['Date'][:4].tolist())
    keys = cycle(dates)
    df['Key'] = [next(keys) for fruit in range(len(df))]
    #print(df[['Reported EPS','Key']])
    # eyoy = df.groupby(['Key'])[
    #     'Reported EPS'].pct_change(-1)*100*np.sign(df['Reported EPS'].shift(-1))

    yoy = df.groupby(['Key'], sort=False)['Reported EPS'].apply(list)
    eyoy = calcPercent(yoy)

    eqoq = (df['Reported EPS'].pct_change(-1)*100 *
            np.sign(df['Reported EPS'].shift(-1)))

    # pct_change(-1)*100*np.sign(df['Actual Revenue'].shift(-1))
    yoy = df.groupby(['Key'], sort=False)['Actual Revenue'].apply(list)

    syoy = calcPercent(yoy)

    sqoq = (df['Actual Revenue'].pct_change(-1)*100 *
            np.sign(df['Actual Revenue'].shift(-1)))
    # print(eqoq.round(0).head(3).tolist())
    # print(eyoy.round(0).head(3).tolist())

    earn = [eqoq.round(0).head(4).tolist(), eyoy]

    sales = [sqoq.round(0).head(4).tolist(), syoy]

    #earn = [[-1234 if isnan(i) else i for i in j ] for j in earn]

    #sales = [[-1234 if isnan(i) else i for i in j ] for j in sales]

    return earn, sales


def calcPercent(yoy):
    arr = []
    for val in yoy:
        if(len(val) > 1):
            pchange = (val[0] - val[1])*100 / abs(val[1])
            arr.append(round(pchange, 0))
    return arr
    # print(sqoq.round(0).head(3).tolist())
    # print(syoy.round(0).head(3).tolist())


def includeEPSRevenue():
    import os
    # Create data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    os.makedirs(data_dir, exist_ok=True)

    mark_scanner_path = os.path.join(data_dir, 'mark_scanner.xlsx')
    exisiting_df = pd.read_excel(mark_scanner_path)
    #print(exisiting_df['Stock'])
    count = 0
    for stock in exisiting_df['Stock']:
        if count > 0 and count % 5 == 0:
            print("Entering wait time of 60 min")
            time.sleep(60)
            print("Ending wait time of 60 min")
        print(stock)
        print(getEarningsData(stock))
        count = count + 1
        if count > 0 and count % 5 == 0:
            print("Entering wait time of 60 min")
            time.sleep(60)
            print("Ending wait time of 60 min")
        print(getRevenueData(stock))
        count = count + 1
        print(count)
    return exisiting_df


# includeEPSRevenue()

#print(5%5)

def getStockSummary(ticker):
    try:
        # replace the "demo" apikey below with your own key from https://www.alphavantage.co/support/#api-key
        url = 'https://www.alphavantage.co/query?function=OVERVIEW&symbol=' + \
            ticker+'&apikey='+key
        #url = 'https://www.alphavantage.co/query?function=OVERVIEW&symbol=IBM&apikey=demo'
        r = requests.get(url)
        data = r.json()
        print(data["Sector"], ':', data["Industry"])
        description = data["Description"]
        print(description)
    except Exception as ex:
        print(ex)

#getStockSummary('AAPL')


def getStockNews(ticker):
    try:
        url = 'https://www.alphavantage.co/query?function=NEWS_SENTIMENT&tickers=' + \
            ticker+'&apikey='+key
        r = requests.get(url)
        data = r.json()
        print(data)
    except Exception as ex:
        print(ex)

#getStockNews('SQM')
