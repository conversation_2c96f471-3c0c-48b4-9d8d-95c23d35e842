# Sample .env file for PythonicFin on Raspbian Linux
# Copy this file to the project root directory as .env and customize as needed

# Fidelity settings
FIDELITY_FILE=path/to/your/fidelity/file.xlsx

# Alpha Vantage API key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key

# Yahoo Finance settings
# User agent for Yahoo Finance requests
YF_USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36

# Path settings
# Adjust these paths as needed for your Raspbian environment
DATA_DIR=src/data
SCREENER_DIR=src/data/screener
IMAGES_DIR=src/data/images
