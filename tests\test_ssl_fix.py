"""
Test script to verify that the SSL certificate verification fix works.
This script tests downloading data for tickers that were previously failing due to SSL certificate issues.
"""

import sys
import os

# Add src directory to Python path
sys.path.append('src')

import yfinance as yf
from common.http_session import get_session

def test_download_with_ssl_fix(ticker):
    """Test downloading data with SSL verification disabled."""
    print(f"\nTesting yf.download() with ticker '{ticker}'...")

    # Create a session with custom SSL configuration
    session = get_session('src/certs/my_ca_bundle.pem')

    # Try to download data for the ticker
    try:
        print(f"Attempting to download data for {ticker}...")
        df = yf.download(ticker, period='1mo', session=session, progress=True)

        if df is not None and not df.empty:
            print(f"Success! Downloaded {len(df)} rows of data for {ticker}")
            print("\nFirst 3 rows:")
            print(df.head(3))
            return True
        else:
            print(f"Error: No data downloaded for {ticker}")
            return False

    except Exception as e:
        print(f"Error downloading {ticker}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_tickers():
    """Test downloading data for multiple tickers."""
    tickers = ['MMM', 'AAPL', 'MSFT', '^GSPC']
    results = {}

    for ticker in tickers:
        results[ticker] = test_download_with_ssl_fix(ticker)

    # Print summary
    print("\n=== TEST SUMMARY ===")
    for ticker, success in results.items():
        print(f"{ticker}: {'SUCCESS' if success else 'FAILURE'}")

    # Return True if all tests passed
    return all(results.values())

if __name__ == "__main__":
    print("Starting SSL fix test with multiple tickers...")
    result = test_multiple_tickers()
    print(f"\nOverall test result: {'SUCCESS' if result else 'FAILURE'}")
