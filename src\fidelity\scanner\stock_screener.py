# stock_screener.py - Handles API requests to Fidelity's stock screener

import requests
from bs4 import BeautifulSoup
from fidelity.common.api_headers import get_screnner_headers # Updated import to new file name
from dotenv import load_dotenv
from util.util import read_and_filter_csv # Keep absolute import
import pandas as pd

load_dotenv()

from fidelity.common.query_builder import getQuery as fq # Updated import to new file name



url = "https://research2.fidelity.com/pi/stock-screener/LoadResults"



headers = get_screnner_headers()


def intra_screen(rvol):

 payload = fq(rvol)

 response = requests.request("POST", url, headers=headers, data=payload)

 resp = response.json()

 # # Convert HTML to DataFrame
 # df = pd.read_html(resp["html"])[0]

 # print(df)

 # df.to_excel("testing.xlsx")

 # Parse HTML content
 soup = BeautifulSoup(resp["html"], "html.parser")

 # Find all stock symbols
 symbols = []
 for row in soup.find_all("tr"):
     symbol = row.find("a", class_="symbol strong")
     if symbol:
         symbols.append(symbol.text.split("/")[0])  # Remove '/' and everything after

 #print(len(symbols))

 rs_filtered_df = read_and_filter_csv(78)

 # Convert the 'Ticker' column to a list
 ticker_list = rs_filtered_df['Ticker'].tolist()

 # Filter symbols that are present in ticker_list using list comprehension
 present_symbols = [symbol for symbol in symbols if symbol in ticker_list]

 return present_symbols

#intra_screen(.7)
