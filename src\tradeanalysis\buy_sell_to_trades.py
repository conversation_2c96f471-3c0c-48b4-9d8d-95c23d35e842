#!/usr/bin/python
# -*- coding: utf-8 -*-
import pandas as pd
import re


def generateTrades(df, symbol):
    df = df.loc[df['Symbol'] == symbol]

    tradesdf = pd.DataFrame(columns=[
        'BuyDate',
        'SellDate',
        'Quantity',
        'BuyPrice',
        'SellPrice',
        'P/L',
        '$Transaction',
        ])

    # print(df.info())

    convert_dict = {'Quantity': int, 'Amount': float}
    df = df.astype(convert_dict)
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.sort_values(by='Date')

    # df.to_csv(os.path.join(data_dir, 'cleaned1.csv'))

    start = 0
    totalbought = 0
    totalsold = 0
    buycost = 0
    sellcost = 0

    # profit = 0

    startDate = ''
    endDate = ''
    if len(df) == 1:
        row = df.iloc[0]
        if row['Quantity'] > 0:

            # means bought

            dict = {
                'Symbol': row['Symbol'],
                'BuyDate': row['Date'],
                'SellDate': row['Date'],
                'Quantity': row['Quantity'],
                'BuyPrice': row['Price'],
                'SellPrice': 0,
                'P/L': row['Amount'],
                '$Transaction': row['Amount'],
                }
        else:

               # sold

            dict = {
                'Symbol': row['Symbol'],
                'BuyDate': row['Date'],
                'SellDate': row['Date'],
                'Quantity': row['Quantity'],
                'BuyPrice': 0,
                'SellPrice': row['Price'],
                'P/L': row['Amount'],
                '$Transaction': row['Amount'],
                }
        tradesdf = pd.concat([tradesdf, pd.DataFrame(dict,index=[0])], ignore_index=True)#tradesdf.append(dict, ignore_index=True)
    else:
        for (index, row) in df.iterrows():
            if start == 0:

             # nothing started yet

                start = row['Quantity']
                startDate = row['Date']
                if start > 0:
                    buycost = row['Amount']
                    totalbought = start
                else:
                    sellcost = row['Amount']
                    totalsold = start
            else:

              # print('started',start)

                curr = row['Quantity']
                if curr > 0:
                    totalbought = totalbought + curr
                    buycost = buycost + row['Amount']
                else:
                    totalsold = totalsold + curr
                    sellcost = sellcost + row['Amount']

                if start + curr == 0:  # trade closed

                  # print('matched',start,curr)

                    endDate = row['Date']
                    avgbuycost = buycost / totalbought
                    avgsellcost = sellcost / (totalsold * -1)

                  # print('closed', start, curr,avgbuycost,avgsellcost)
                #   if curr < 0 :
                #       #print('flipped curr')
                #       curr = curr * -1
                  # print((avgbuycost+avgsellcost)* curr)

                    profit = (avgbuycost + avgsellcost) \
                        * abs(totalbought)
                    dict = {
                        'Symbol': row['Symbol'],
                        'BuyDate': startDate,
                        'SellDate': endDate,
                        'Quantity': totalbought,
                        'BuyPrice': abs(avgbuycost),
                        'SellPrice': abs(avgsellcost),
                        'P/L': profit,
                        '$Transaction': buycost,
                        }
                    tradesdf =  pd.concat([tradesdf, pd.DataFrame(dict,index=[0])], ignore_index=True)#tradesdf.append(dict, ignore_index=True)
                    start = 0
                    totalsold = 0
                    totalbought = 0
                    buycost = 0
                    sellcost = 0
                else:

                    # sum to start

                    start = start + curr
    if start != 0:
        print ('mismatch:', symbol)
        if row['Quantity'] > 0:
            avgbuycost = round(buycost / totalbought, 2)

            # means bought

            dict = {
                'Symbol': row['Symbol'],
                'BuyDate': row['Date'],
                'SellDate': row['Date'],
                'Quantity': totalbought,
                'BuyPrice': avgbuycost,
                'SellPrice': 0,
                'P/L': totalbought * avgbuycost,
                '$Transaction': totalbought * avgbuycost,
                }
        else:

               # sold

            avgsellcost = round(sellcost / (totalsold * -1), 2)
            dict = {
                'Symbol': row['Symbol'],
                'BuyDate': row['Date'],
                'SellDate': row['Date'],
                'Quantity': totalsold,
                'BuyPrice': 0,
                'SellPrice': avgsellcost,
                'P/L': totalsold * avgsellcost * -1,
                '$Transaction': totalsold * avgsellcost,
                }
        tradesdf = pd.concat([tradesdf, pd.DataFrame(dict,index=[0])], ignore_index=True)#tradesdf.append(dict, ignore_index=True)
    return tradesdf

# Regex pattern for stock symbols (e.g., uppercase letters, possibly with digits)
stock_pattern = r'^[A-Z]{1,5}$'  # Adjust pattern as necessary for your criteria

# Use relative path for data files
import os

# Create data directory if it doesn't exist
data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
os.makedirs(data_dir, exist_ok=True)

# Path to activity CSV file
activity_csv = os.path.join(data_dir, 'activity.csv')

df = \
    pd.read_csv(activity_csv
                , engine='python',
                skiprows=6).dropna().replace({'\$': '', ',': ''},
        regex=True)
df['Quantity'] = df['Quantity'].str.replace(',', '')
df['Quantity'] = df['Quantity'].astype(float).astype(int)  # Converts to float first, then int

# Filter using regex to match stock-like symbols
df = df[df['Symbol'].apply(lambda x: re.match(stock_pattern, x) is not None)]

print(df)

# df.to_csv(os.path.join(data_dir, 'cleaned.csv'))

op = pd.DataFrame()
symbols = df.Symbol.unique()

for symbol in symbols:
    op =  pd.concat([op, generateTrades(df, symbol=symbol)], ignore_index=True)#op.append(generateTrades(df, symbol=symbol))

# df = generateTrades(df,'AAPL')

op.sort_values(by='BuyDate', ascending=False, inplace=True)
op.reset_index(drop=True, inplace=True)


# Apply the style to series using df.style.apply() method and the function we defined

# op.to_csv(os.path.join(data_dir, 'trades.csv'))

def highlight_col(x):

    # copy df to new - original data are not changed

    df = x.copy()

    # set by condition

    profit = df['P/L'] > 0
    loss = df['P/L'].between(-999, 0)
    bigloss = df['P/L'] <= -999
    df.loc[profit, :] = 'background-color: green'
    df.loc[loss, :] = 'background-color: orange'
    df.loc[bigloss, :] = 'background-color: red'
    return df


html_column = op.style.apply(highlight_col, axis=None)

html_column.to_excel(os.path.join(data_dir, 'trades.xlsx')
                     , engine='openpyxl')

# group_df = df.groupby('Symbol')

# symbol = 'AAPL'

# quantity = df.groupby('Symbol')['Quantity'].apply(list)[symbol]

# print(quantity)

# price = df.groupby('Symbol')['Price'].apply(list)[symbol]

# print(price)

# amount = df.groupby('Symbol')['Amount'].apply(list)[symbol]

# print(amount)

# date = df.groupby('Symbol')['Date'].apply(list)[symbol]

# print(date)
