from util.util import normalizeDF
from stockdata.data_source import getBasicStockData
import statsmodels.api as sm
from statsmodels import regression


def linreg(x, y):
    alpha = 0
    beta = 0
    try:
        x = sm.add_constant(x)
        model = regression.linear_model.OLS(y, x).fit()
        # We are removing the constant
        x = x[:, 1]
        alpha = model.params[0]
        beta = model.params[1]
        #model.params[0], model.params[1]
    except:
       print("An exception occurred when calculating beta")
    return alpha, beta


def getAlphaBeta(ticker1, ticker2):

    print("calculating beta:", ticker1)

    df1 = getBasicStockData(ticker1)
    print(df1)
    df2 = getBasicStockData(ticker2)

    df1, df2 = normalizeDF(df1, df2)

    #print(df1, df2)

    return_ticker1 = df1.Close.pct_change()[1:]
    return_ticker2 = df2.Close.pct_change()[1:]

    X = return_ticker1.values
    Y = return_ticker2.values

    # alpha, beta = linreg(X,Y)
    # print('alpha: ' + str(alpha))
    # print('beta: ' + str(beta))

    return linreg(X, Y)


print(getAlphaBeta("SPY", "MU"))
