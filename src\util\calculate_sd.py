from stockdata.data_source import getPCRatioData
from util.util import calcSdWeight
import numpy as np
import pandas as pd
from util.scrapper import CNNFearGreed, getCurrentPCEData

# rolling 6m/ one year
period = 126  # 252


def equityCBOEPCESd():
    # getPCRatios()
    PCRatio = getPCRatioData()
    val = 0
    try:
        val = getCurrentPCEData()
        PCRatio.insert(0,val)
    except:
        print("Error in getting recent PCE")
    #print(PCRatio)
    return calcSdWeight(PCRatio, period)

#print(equityCBOEPCESd())

def squeezemetricsSd(df):
    if not df.empty:
        df.sort_values(by='date', ascending=False, inplace=True)
        dix = calcSdWeight(df['dix'].tolist(), period)
        gex = calcSdWeight(df['gex'].tolist(), period)
        #print(df['gex'][len(df)-1])
        if gex == 3:
            print("Extreme Gamma Reading")
        if df['gex'][len(df)-1] < 0:
            print(
                "Gamma Negative, Room for huge volatality, observe for bottoming out near term")
        return [dix, gex]

#print(squeezemetricsSd(squeezeMetrics()))

def calcSdFromMA(df, length='50'):
    val = df['Close'] - df[length]
    return calcSdWeight(val.tolist(), period)


def calcSdATR(df):
    val = calcSdWeight(df['ATR'].tolist(), period)
    if val == 0:
        return 1
    else:
        return val


def calcVoltality(df):
    # calculate daily logarithmic return
    df['returns'] = (np.log(df.Close /
                            df.Close.shift(-1)))
    # calculate daily standard deviation of returns
    daily_std = np.std(df.returns)
    # period daily standard deviation
    return round((daily_std * period ** 0.5)*100)


def calSDVix(df):
    #print(df)
    # Handle MultiIndex columns if present
    if isinstance(df.columns, pd.MultiIndex):
        # Get the Close column using the MultiIndex
        close_values = df[('Close', df.columns.get_level_values('Ticker')[0])].values.tolist()
        return calcSdWeight(close_values, period)
    else:
        # Regular DataFrame
        return calcSdWeight(df['Close'].tolist(), period)

def calSDBondRatio(df,period):

  return calcSdWeight(df['Close'].tolist(), period)


def calCNNFearGreedIndex():
 try:
    val = CNNFearGreed()
    if 80 <= val <= 100 or 0 <= val <= 20:
        return 3
    elif 30 < val < 70:
        return 1
    else:
        return 2
 except:
    print('Error in CNN Fear and Greed')


#print(calSDVix(getStockData('^VIX')))
#print(calCNNFearGreedIndex())
