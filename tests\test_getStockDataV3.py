"""
Test script for getStockDataV3 function with the latest yfinance version.
This script tests if the function can successfully retrieve stock data.
"""

import sys
import os
import traceback
import asyncio
import aiohttp

# Add src directory to Python path
sys.path.append('src')

from stockdata.data_source import getStockDataV3

async def test_getStockDataV3():
    """Test the getStockDataV3 function with a known ticker."""
    print("Testing getStockDataV3 with ticker 'AAPL'...")

    try:
        # Get stock data for Apple
        async with aiohttp.ClientSession() as session:
            df = await getStockDataV3(session, 'AAPL')

        if df is not None and not df.empty:
            print("Success! Retrieved data for AAPL:")
            print(f"Shape: {df.shape}")
            print(f"Date range: {df.index.min()} to {df.index.max()}")
            print(f"Columns: {df.columns.tolist()}")
            print("\nFirst 5 rows:")
            print(df.head())
            return True
        else:
            print("Failed to retrieve data for AAPL")
            return False
    except Exception as e:
        print(f"Error occurred: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_getStockDataV3())
