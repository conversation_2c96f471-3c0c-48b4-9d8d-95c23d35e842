"""
Test script to debug the calculateFactors function.
"""

import sys
import os
import traceback

# Add src directory to Python path
sys.path.append('src')

def test_cnn_fear_greed():
    """Test the CNNFearGreed function."""
    try:
        from util.scrapper import CNNFearGreed
        from util.calculate_sd import calCNNFearGreedIndex
        from util.constants import PCECorrelation

        print("\n=== Testing CNN Fear & Greed Index ===")
        try:
            cnn_value = CNNFearGreed()
            print(f"CNN Fear & Greed raw value: {cnn_value}")

            index = calCNNFearGreedIndex()
            print(f"CNN Fear & Greed index: {index}")

            correlation = PCECorrelation[index]
            print(f"CNN Fear & Greed correlation: {correlation}")

            return True
        except Exception as e:
            print(f"Error in CNN Fear & Greed test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_pce_data():
    """Test the PCE data retrieval."""
    try:
        from util.calculate_sd import equityCBOEPCESd
        from util.constants import PCECorrelation

        print("\n=== Testing PCE Data ===")
        try:
            pce_index = equityCBOEPCESd()
            print(f"PCE index: {pce_index}")

            correlation = PCECorrelation[pce_index]
            print(f"PCE correlation: {correlation}")

            return True
        except Exception as e:
            print(f"Error in PCE data test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_squeeze_metrics():
    """Test the squeezeMetrics function."""
    try:
        from util.scrapper import squeezeMetrics
        from util.calculate_sd import squeezemetricsSd
        from util.constants import DIXGEXCorrelation

        print("\n=== Testing Squeeze Metrics ===")
        try:
            squeeze_data = squeezeMetrics()
            print(f"Squeeze data shape: {squeeze_data.shape}")
            print(f"Squeeze data columns: {squeeze_data.columns}")
            print(f"First few rows of squeeze data:\n{squeeze_data.head()}")

            dixgex = squeezemetricsSd(squeeze_data)
            print(f"DIX/GEX indices: {dixgex}")

            dix_correlation = DIXGEXCorrelation[dixgex[0]]
            gex_correlation = DIXGEXCorrelation[dixgex[1]]
            print(f"DIX correlation: {dix_correlation}")
            print(f"GEX correlation: {gex_correlation}")

            return True
        except Exception as e:
            print(f"Error in squeeze metrics test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_vix_data():
    """Test the VIX data retrieval."""
    try:
        from stockdata.data_source import getStockDataV3
        from util.calculate_sd import calSDVix
        from util.constants import VIXCorrelation

        print("\n=== Testing VIX Data ===")
        try:
            vix_data = getStockDataV3('^VIX')
            print(f"VIX data shape: {vix_data.shape}")
            print(f"VIX data columns: {vix_data.columns}")

            vix_index = calSDVix(vix_data)
            print(f"VIX index: {vix_index}")

            correlation = VIXCorrelation[vix_index]
            print(f"VIX correlation: {correlation}")

            return True
        except Exception as e:
            print(f"Error in VIX data test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_skew_data():
    """Test the SKEW data retrieval."""
    try:
        from stockdata.data_source import getStockDataV3
        from util.calculate_sd import calSDVix
        from util.constants import VIXCorrelation

        print("\n=== Testing SKEW Data ===")
        try:
            skew_data = getStockDataV3('^SKEW')
            print(f"SKEW data shape: {skew_data.shape}")
            print(f"SKEW data columns: {skew_data.columns}")

            skew_index = calSDVix(skew_data)
            print(f"SKEW index: {skew_index}")

            correlation = VIXCorrelation[skew_index]
            print(f"SKEW correlation: {correlation}")

            return True
        except Exception as e:
            print(f"Error in SKEW data test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_merc_retro():
    """Test the Mercury Retrograde function."""
    try:
        from indicators.merc_retro import mercRetroWeight
        from util.constants import VIXCorrelation

        print("\n=== Testing Mercury Retrograde ===")
        try:
            merc_index = mercRetroWeight()
            print(f"Mercury Retrograde index: {merc_index}")

            correlation = VIXCorrelation[merc_index]
            print(f"Mercury Retrograde correlation: {correlation}")

            return True
        except Exception as e:
            print(f"Error in Mercury Retrograde test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_ratio_data():
    """Test the ratio data retrieval."""
    try:
        from stockdata.factors import getRatio
        from util.calculate_sd import calSDBondRatio
        from util.constants import VIXCorrelation

        print("\n=== Testing Ratio Data ===")
        try:
            # Test IEI/HYG ratio
            print("Testing IEI/HYG ratio...")
            ratio_data = getRatio('IEI', 'HYG')
            print(f"Ratio data shape: {ratio_data.shape}")
            print(f"Ratio data columns: {ratio_data.columns}")

            ratio_index = calSDBondRatio(ratio_data, 20)
            print(f"Ratio index: {ratio_index}")

            correlation = VIXCorrelation[ratio_index]
            print(f"Ratio correlation: {correlation}")

            return True
        except Exception as e:
            print(f"Error in ratio data test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def test_calculate_factors():
    """Test the calculateFactors function."""
    try:
        from stockdata.factors import calculateFactors

        print("\n=== Testing calculateFactors ===")
        try:
            factors = calculateFactors()
            print(f"Calculated factors: {factors}")

            return True
        except Exception as e:
            print(f"Error in calculateFactors test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        return False

def main():
    """Main function to run the tests."""
    print("=== Testing Risk on/off Factor Calculation ===")

    # Run the tests
    cnn_result = test_cnn_fear_greed()
    pce_result = test_pce_data()
    squeeze_result = test_squeeze_metrics()
    vix_result = test_vix_data()
    skew_result = test_skew_data()
    merc_result = test_merc_retro()
    ratio_result = test_ratio_data()
    factors_result = test_calculate_factors()

    # Print summary
    print("\n=== TEST SUMMARY ===")
    print(f"CNN Fear & Greed test: {'PASSED' if cnn_result else 'FAILED'}")
    print(f"PCE data test: {'PASSED' if pce_result else 'FAILED'}")
    print(f"Squeeze metrics test: {'PASSED' if squeeze_result else 'FAILED'}")
    print(f"VIX data test: {'PASSED' if vix_result else 'FAILED'}")
    print(f"SKEW data test: {'PASSED' if skew_result else 'FAILED'}")
    print(f"Mercury Retrograde test: {'PASSED' if merc_result else 'FAILED'}")
    print(f"Ratio data test: {'PASSED' if ratio_result else 'FAILED'}")
    print(f"calculateFactors test: {'PASSED' if factors_result else 'FAILED'}")

    # Overall result
    all_passed = all([cnn_result, pce_result, squeeze_result, vix_result, skew_result, merc_result, ratio_result, factors_result])

    print("\n=== OVERALL RESULT ===")
    print(f"All tests: {'PASSED' if all_passed else 'FAILED'}")

    if not all_passed:
        print("\nSome tests failed. Check the output above for details.")

def test_cnn_fear_greed_simple():
    """A simple test for the CNNFearGreed function with SSL certificate handling."""
    try:
        from util.scrapper import CNNFearGreed

        print("\n=== Simple CNN Fear & Greed Test ===")

        # First, check if curl_cffi is installed
        try:
            import curl_cffi
            print("curl_cffi is installed - using browser impersonation approach")
        except ImportError:
            print("curl_cffi is not installed - will fall back to requests with SSL verification disabled")
            print("To install curl_cffi: pip install curl_cffi")

        try:
            # Call the function
            value = CNNFearGreed()
            print(f"CNN Fear & Greed value: {value}")

            # Check if the value is a number
            if isinstance(value, (int, float)):
                print("✓ Value is numeric")
            else:
                print(f"✗ Value is not numeric: {type(value)}")
                return False

            # Check if the value is within the expected range (0-100)
            if 0 <= value <= 100:
                print("✓ Value is within expected range (0-100)")
            else:
                print(f"✗ Value is outside expected range: {value}")
                return False

            # If we got a non-zero value, the test is successful
            if value > 0:
                print("✓ Successfully retrieved a non-zero value")
            else:
                print("⚠ Retrieved a zero value, which might indicate an error")

            return True
        except Exception as e:
            print(f"Error in simple CNN Fear & Greed test: {e}")
            traceback.print_exc()
            return False
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure to install required packages: pip install requests")
        return False

if __name__ == "__main__":
    # Run just the simple test
    test_cnn_fear_greed_simple()

    # Or run all tests
    # main()
