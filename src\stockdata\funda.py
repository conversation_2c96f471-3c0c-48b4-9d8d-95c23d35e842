from yahoofinancials import YahooFinancials as YF
import pandas as pd
from nested_lookup import get_all_keys,nested_lookup
from dateutil.parser import parse

def is_date(string, fuzzy=False):
    """
    Return whether the string can be interpreted as a date.

    :param string: str, string to check for date
    :param fuzzy: bool, ignore unknown tokens in string if True
    """
    try: 
        parse(string, fuzzy=fuzzy)
        return string

    except ValueError:
        return 'nad'

tick = YF('AAPL')
data = tick.get_financial_stmts(frequency='quaterly',statement_type='income')#['incomeStatementHistoryQuarterly']['U'].keys()
keys = get_all_keys(data)
print(keys)
#print(keys)
dt_dates = [is_date(date, fuzzy=False)  for date in keys]
newlist = newlist = [x for x in dt_dates if x != 'nad']
df = pd.DataFrame()
for val in newlist:
    results = nested_lookup(key = val,document = data,wild = True)
    results[0]['Date'] = val
    df = df.append(results)
print(list(df.columns.values))
df.set_index("Date", inplace = True)
op = df[['totalRevenue','incomeBeforeTax']].sort_values(by='Date')
print(op)