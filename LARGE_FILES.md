# Large Files Documentation

This document explains the large files that were previously tracked in the Git repository and why they were included.

## Browser Automation Tools

### PhantomJS (phantomjs.exe, phantomjs-2.1.1-windows.zip)
- **Size**: ~37 MB combined
- **Purpose**: Headless browser used for web scraping and automation
- **Why it was included**: Likely included to ensure all developers had the same version for consistent automation results
- **Better approach**: Download from official sources or use package managers; document version requirements

### ChromeDriver (chromedriver.exe, chromedriver_win32.zip)
- **Size**: ~18 MB combined
- **Purpose**: WebDriver for Chrome browser automation with Selenium
- **Why it was included**: Ensures compatible version with the code
- **Better approach**: Use WebDriverManager in Python to automatically download the correct version

### GeckoDriver (geckodriver.exe)
- **Size**: ~3.5 MB
- **Purpose**: WebDriver for Firefox browser automation with Selenium
- **Why it was included**: Ensures compatible version with the code
- **Better approach**: Use WebDriverManager in Python to automatically download the correct version

## Browser Extensions

### AdGuard AdBlocker (adguard_adblocker-4.0.181.xpi)
- **Size**: ~10 MB
- **Purpose**: Browser extension to block ads during web scraping
- **Why it was included**: May have been used to improve scraping reliability
- **Better approach**: Document as a requirement or use headless browsing options

## Database and Cache Files

### SQLite Database (my_cache.sqlite)
- **Size**: ~36 MB (multiple instances)
- **Purpose**: Cache for API responses or scraped data
- **Why it was included**: Likely committed accidentally
- **Better approach**: Use .gitignore to exclude database files; implement proper data persistence strategy

## Documentation and Sample Data

### TruffleHog Scan (trufflehog_scan.txt)
- **Size**: ~19 MB
- **Purpose**: Output from security scanning tool to find secrets in code
- **Why it was included**: Documentation of security audit
- **Better approach**: Store scan results outside of repository or in a compressed format

### Excel Files (various .xlsx files)
- **Size**: ~10 MB combined
- **Purpose**: Financial data, stock lists, and analysis templates
- **Why it was included**: Sample data for analysis functions
- **Better approach**: Use smaller sample datasets or generate synthetic data

### Word Documents (various .docx files)
- **Size**: ~10 MB combined
- **Purpose**: Documentation and analysis reports
- **Why it was included**: Project documentation
- **Better approach**: Convert to Markdown for better version control

## API and Integration Files

### Alpaca Postman Collection (alpaca-postman-master.zip)
- **Size**: ~1.6 MB
- **Purpose**: Postman collection for Alpaca API testing
- **Why it was included**: API integration reference
- **Better approach**: Document API endpoints in Markdown or provide link to official resources

## Python Learning Resources

### Python For Finance (Python For Finance-20210821T133134Z-001.zip)
- **Size**: ~10.8 MB
- **Purpose**: Learning materials for financial analysis with Python
- **Why it was included**: Reference materials for development
- **Better approach**: Document resources with links to external sources
