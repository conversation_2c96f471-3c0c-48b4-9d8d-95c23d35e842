import pandas as pd
import pandas_market_calendars as mcal

def cleanUpPCData():
    df = pd.read_csv('src/data/PC_Ratio.csv').drop_duplicates(subset=['Date'],keep='last')
    #print(df)
    file_name = "src/data/PC_Ratio.csv"
    df.to_csv(file_name,index=False) # you don't need to set sep in this because to_csv makes it comma delimited.


def cleanUpHolidays():
   # Create a calendar
   nyse = mcal.get_calendar('NYSE')
   # Show available calendars

   print(pd.to_datetime(nyse.holidays()))

#cleanUpHolidays()
cleanUpPCData()
# arr = ['XLY','XLC','XLK','XLI','XLB','XLE','XLP','XLV','XLU','XLF','XLRE','XBI','SMH']

# for item in arr:
#     print('https://stockcharts.com/c-sc/sc?s='+item+'%3A%24SPX&p=D&st=2005-01-01&i=t7012319603c&r=tdy')
