# Core data processing and analysis
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.4.0
seaborn>=0.11.1
scikit-learn>=1.0.0
yfinance==0.1.83  # This specific version is required for compatibility
pandas-ta==0.3.14b0
pandas_datareader>=0.10.0
yahoo_fin>=0.8.9

# Web and API interaction
requests>=2.25.0
beautifulsoup4>=4.9.0
python-dotenv>=0.20.0
diskcache>=5.0.0  # For caching web requests

# Document handling
python-docx>=0.8.0
openpyxl>=3.0.0
xlrd>=2.0.0  # For reading older Excel files

# Financial data and trading
backtrader>=1.9.0
exchange-calendars>=3.0.0
robin-stocks>=2.0.0  # For Robinhood API access

# Utilities
pytz>=2021.0
tqdm>=4.60.0
python-dateutil>=2.8.0
