#!/usr/bin/env python3
"""
Standalone Script for download_stock_charts Function

This script loads screener data from Excel files in the data/screener directory
and uses the existing download_stock_charts function to download charts.

Usage:
    python src/standalone_download_stock_charts.py

The script will:
1. Look for Excel files in data/screener directory
2. Let you select which file to use
3. Prompt for chart IDs and document name
4. Call the existing download_stock_charts function
"""

import os
import sys
import pandas as pd

# Add src directory to Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__)))

try:
    from fidelity.chart_analysis.chart_downloader import download_stock_charts
except ImportError as e:
    print(f"Error importing download_stock_charts: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


def get_screener_file():
    """Ask user for the screener file name"""
    print("Enter the path to your screener Excel file.")
    print("Examples:")
    print("  - data/screener/screener_results.xlsx")
    print("  - C:/Users/<USER>/Desktop/pythonicfin/data/screener/my_screener.xlsx")
    print("  - screener_results.xlsx")

    while True:
        file_path = input("\nEnter file path: ").strip()

        if not file_path:
            print("Please enter a file path.")
            continue

        # Remove quotes if user added them
        file_path = file_path.strip('"').strip("'")

        if os.path.exists(file_path):
            return file_path
        else:
            print(f"File not found: {file_path}")
            print("Please check the path and try again.")


def load_screener_data(file_path):
    """Load screener data from Excel file"""
    try:
        print(f"Loading data from: {file_path}")

        # Try to read the Excel file
        df = pd.read_excel(file_path)

        print(f"Loaded {len(df)} rows and {len(df.columns)} columns")

        # Check if required 'Symbol' column exists
        if 'Symbol' not in df.columns:
            print("Warning: 'Symbol' column not found in the data")
            print("Available columns:", list(df.columns))

            # Try to find a column that might contain symbols
            symbol_candidates = [col for col in df.columns if 'symbol' in col.lower() or 'ticker' in col.lower()]
            if symbol_candidates:
                print(f"Possible symbol columns: {symbol_candidates}")
                col_choice = input(f"Enter column name to use as Symbol column: ").strip()
                if col_choice in df.columns:
                    df = df.rename(columns={col_choice: 'Symbol'})
                else:
                    print(f"Column '{col_choice}' not found")
                    return None
            else:
                return None

        # Show sample data
        print(f"\nFirst few symbols: {df['Symbol'].head().tolist()}")
        print(f"Total unique symbols: {df['Symbol'].nunique()}")

        return df

    except Exception as e:
        print(f"Error loading Excel file: {e}")
        return None


def get_chart_parameters():
    """Get chart IDs and document name from user"""
    print("\n=== Chart Download Parameters ===")

    print("\nTo get chart IDs:")
    print("1. Go to StockCharts.com")
    print("2. Create a chart with your desired settings")
    print("3. Copy the 'i=' parameter from the URL (e.g., t12345678901)")

    while True:
        daily_id = input("\nEnter daily chart ID (from StockCharts URL): ").strip()
        if daily_id:
            break
        print("Daily chart ID is required.")

    while True:
        wkly_id = input("Enter weekly chart ID (from StockCharts URL): ").strip()
        if wkly_id:
            break
        print("Weekly chart ID is required.")

    while True:
        doc_name = input("Enter output document name (without .docx): ").strip()
        if doc_name:
            break
        print("Document name is required.")

    clean_image_input = input("Clean image? (y/n): ").strip().lower()
    clean_image = clean_image_input in ['y', 'yes']

    return daily_id, wkly_id, doc_name, clean_image


def main():
    """Main function"""
    print("=== Standalone download_stock_charts Script ===\n")

    # Step 1: Get screener file
    file_path = get_screener_file()
    if not file_path:
        print("No valid screener file selected. Exiting.")
        return

    # Step 2: Load screener data
    screenerdf = load_screener_data(file_path)
    if screenerdf is None:
        print("Failed to load screener data. Exiting.")
        return

    # Step 3: Get chart parameters
    daily_id, wkly_id, doc_name, clean_image = get_chart_parameters()

    # Step 4: Show summary and confirm
    print(f"\n=== Summary ===")
    print(f"Screener file: {file_path}")
    print(f"Number of stocks: {len(screenerdf)}")
    print(f"Daily Chart ID: {daily_id}")
    print(f"Weekly Chart ID: {wkly_id}")
    print(f"Document Name: {doc_name}")
    print(f"Clean Image: {clean_image}")

    confirm = input("\nProceed with chart download? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("Download cancelled.")
        return

    # Step 5: Call the existing download_stock_charts function
    print(f"\nStarting chart download for {len(screenerdf)} stocks...")
    try:
        download_stock_charts(screenerdf, doc_name, daily_id, wkly_id, clean_image)
        print("\n✓ Chart download completed successfully!")
    except Exception as e:
        print(f"\n✗ Error during chart download: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
