import pandas as pd
import numpy as np
from tabulate import tabulate
from typing import List, Dict, Any, Tuple # Import necessary types

# (No direct imports from sibling modules needed here typically,
# as this module receives data to display)

def display_ticker_info(description: str, sector: str, industry: str, earnings_date: str, market_cap: str):
    """Displays basic ticker information."""
    print("\n--- Ticker Information ---")
    print(f"Description: {description[:150]}...") # Limit description length
    print(f"Sector: {sector}")
    print(f"Industry: {industry}")
    print(f"Earnings Date: {earnings_date}")
    print(f"Market Cap: {market_cap}")

def display_yahoo_news(news_list: List[Dict[str, Any]]):
    """Displays recent news headlines from Yahoo Finance."""
    print("\n--- Recent News (Yahoo) ---")
    if news_list:
        news_count = 0
        for item in news_list:
            if news_count < 5:
                title = item.get('title', 'N/A')
                link = item.get('link', '#')
                print(f"- {title} ({link})")
                news_count += 1
            else:
                break
        if news_count == 0: # Should not happen if news_list is not empty, but good practice
            print("No recent news found via yfinance.")
    else:
        print("No recent news found via yfinance.")

def display_robinhood_catalyst_news(news_list: List[Dict[str, Any]]):
    """Displays today's news from Robinhood."""
    print("\n--- Catalyst News (Today - Robinhood) ---")
    if not news_list:
        print("No news published today found from Robinhood.")
        return

    for news in news_list[:5]: # Limit to 5
        title = news.get('title', 'N/A')
        preview = news.get('preview_text', 'N/A')
        print(f"- {title}")
        print(f"  {preview}")
        print('---')

def display_atr_sma_metrics(metrics: Dict[str, Any]):
    """Displays ATR%, % Gain From SMA, and ATR% Multiple."""
    if metrics:
        print(f"ATR%: {metrics.get('atr_percentage', 0.0):.2f}%")
        print(f"% Gain From {metrics.get('sma_period', 50)}-MA: {metrics.get('gain_from_sma', 0.0):.2f}%") # Assuming sma_period is passed if needed
        print(f"ATR% Multiple from {metrics.get('sma_period', 50)}-MA: {metrics.get('atr_multiple_from_sma', 0.0):.2f}")
    else:
        print("ATR%/SMA metrics not available.")


def display_rvol(data: pd.DataFrame):
    """Displays the latest RVOL values."""
    if not data.empty and 'rvol' in data.columns:
        latest_rvol = data['rvol'].tail(5).values
        # Format NaN values for display
        formatted_rvol = [f"{x:.2f}" if pd.notna(x) else "N/A" for x in latest_rvol]
        print('RVOL (last 5):', formatted_rvol)
    else:
        print("RVOL data not available.")

def display_atr_change(data: pd.DataFrame):
    """Displays the latest ATR price change values."""
    if not data.empty and 'price_change_atr' in data.columns:
        latest_atr_change = data['price_change_atr'].tail(5).values
        formatted_atr_change = [f"{x:.2f}" if pd.notna(x) else "N/A" for x in latest_atr_change]
        print('ATR Price Change (abs, last 5):', formatted_atr_change)
    else:
        print("ATR Price Change data not available.")

def display_rsi(rsi_series: pd.Series):
    """Displays the latest RSI values."""
    if not rsi_series.empty:
        # Round the RSI values to 2 decimal places
        rounded_rsi_values = np.round(rsi_series.tail(5).values, 2)
        formatted_rsi = [f"{x:.2f}" if pd.notna(x) else "N/A" for x in rounded_rsi_values]
        print('RSI (last 5):', formatted_rsi)
    else:
        print("RSI data not available.")

def display_highs_lows(data: pd.DataFrame):
     """Displays historical highs/lows relative to the current price."""
     print("\n--- Highs & Lows ---")
     if data.empty or 'close_price' not in data.columns:
         print("Insufficient data for highs/lows calculation.")
         return

     try:
         # Ensure index is datetime
         if not isinstance(data.index, pd.DatetimeIndex):
              data.index = pd.to_datetime(data.index)

         # Calculate the highs and lows for each timeframe
         timeframes = {'1w': '7D', '1m': '30D', '3m': '90D', '6m': '180D', '12m': '365D'}
         results = {}
         current_price = data['close_price'].iloc[-1]

         for tf, duration in timeframes.items():
             if len(data) > 1:
                  high_prices = data['close_price'].rolling(window=duration, min_periods=1).max().shift(1)
                  low_prices = data['close_price'].rolling(window=duration, min_periods=1).min().shift(1)
                  prev_high = high_prices.iloc[-1] if not high_prices.empty and pd.notna(high_prices.iloc[-1]) else 'N/A'
                  prev_low = low_prices.iloc[-1] if not low_prices.empty and pd.notna(low_prices.iloc[-1]) else 'N/A'
             else:
                  prev_high = 'N/A'
                  prev_low = 'N/A'

             is_high = False
             is_low = False
             if isinstance(prev_high, (int, float)):
                 is_high = current_price >= prev_high
             if isinstance(prev_low, (int, float)):
                 is_low = current_price <= prev_low

             results[tf] = {
                 'high': prev_high,
                 'low': prev_low,
                 'is_high': is_high,
                 'is_low': is_low
             }

         # Create a DataFrame for display
         df = pd.DataFrame(results).T
         df.columns = ["Prev High", "Prev Low", ">= Prev High", "<= Prev Low"]

         # Format numbers and boolean strings
         df["Prev High"] = df["Prev High"].apply(lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x)
         df["Prev Low"] = df["Prev Low"].apply(lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x)
         # Fix for FutureWarning about downcasting in replace
         # First convert to string to avoid downcasting warning
         df = df.astype(str).replace({'True': 'True', 'False': 'False'})

         print(tabulate(df, headers='keys', tablefmt='fancy_grid'))

     except Exception as e:
         print(f"Error displaying highs/lows: {e}")


def display_mas(data: pd.DataFrame):
    """Displays the latest Moving Average values and conditions."""
    print("\n--- Moving Averages ---")
    if data.empty or not all(col in data.columns for col in ['close_price', 'ema8', 'ema20', 'sma50', 'sma200']):
        print("Insufficient data for MA display.")
        return

    try:
        latest_close_price = data['close_price'].iloc[-1]
        latest_ema8 = data['ema8'].iloc[-1]
        latest_ema20 = data['ema20'].iloc[-1]
        latest_sma50 = data['sma50'].iloc[-1]
        latest_sma200 = data['sma200'].iloc[-1]

        # Get boolean conditions from the last row
        conditions = data[['is_above_ema8', 'is_above_ema20', 'is_above_sma50', 'is_above_sma200']].iloc[-1].tolist()
        # Format booleans as strings
        condition_strs = [str(c) for c in conditions]

        # Create a DataFrame for display
        result_df = pd.DataFrame({
            'Metric': ['close_price', 'ema8', 'ema20', 'sma50', 'sma200'],
            'Value': [latest_close_price, latest_ema8, latest_ema20, latest_sma50, latest_sma200],
            'Is Close Above?': ['N/A'] + condition_strs # Add placeholder for close_price row
        })

        # Format 'Value' column to 2 decimal places, handle potential NaNs
        result_df['Value'] = result_df['Value'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "N/A")

        print(tabulate(result_df, tablefmt='fancy_grid', headers='keys', showindex=False))

    except Exception as e:
        print(f"Error displaying MAs: {e}")


def display_position_sizing_results(entry_price: float, stop_price: float, risk_per_share: float, position_configs: List[Tuple[str, float]], stops_data: Dict[str, List[Tuple[int, float]]]):
    """Displays the calculated position sizing metrics and stop loss plan."""
    print('\n--- Position Sizing ---')
    print(f"Entry Price: {entry_price:.2f}")
    print(f"Stop Price: {stop_price:.2f}")
    print(f"Risk per Share: {risk_per_share:.2f}")

    # --- Display Sizing Metrics ---
    metrics_data = []
    stop_prices_set = set() # Collect unique stop prices across all tiers/sizes

    for name, size in position_configs:
        rounded_size = round(size)
        capital_req = round(rounded_size * entry_price)
        metrics_data.append({
            'Position': name,
            'Shares': rounded_size,
            'Capital Req.': f"${capital_req:,.0f}" # Formatted capital
        })
        # Collect stop prices from this size's tiers
        pos_stops = stops_data.get(name, [])
        for _, price in pos_stops:
            stop_prices_set.add(price)

    metrics_df = pd.DataFrame(metrics_data)
    print("\nSizing Metrics:")
    print(tabulate(metrics_df, tablefmt='fancy_grid', headers='keys', showindex=False))

    # --- Create Combined Stop Loss and Shares Table ---
    if not stop_prices_set:
        print("\nNo stop loss plan generated (likely 0 shares).")
        return

    stop_prices_list = sorted(list(stop_prices_set), reverse=True)
    combined_stops_data = []

    # Helper function to find shares for a given price in a list of (shares, price) tuples
    def find_shares_for_price(price, stops_list):
        for shares, stop_price_tier in stops_list:
            if abs(stop_price_tier - price) < 0.001: # Compare floats with tolerance
                return shares
        return 0 # Return 0 if price not found for this tier

    for i, price in enumerate(stop_prices_list):
        row_data = {'Stop Level': f'Stop {i+1}', 'Stop Price': f"{price:.2f}"}
        # Add columns for each position size defined in position_configs
        for name, _ in position_configs:
             col_name = f"{name} Shares"
             row_data[col_name] = find_shares_for_price(price, stops_data.get(name, []))
        combined_stops_data.append(row_data)


    combined_stops_df = pd.DataFrame(combined_stops_data)

    # Display combined table
    print("\nStop Loss Plan (Shares to Sell at Each Level):")
    print(tabulate(combined_stops_df, tablefmt='fancy_grid', headers='keys', showindex=False))


def display_holdings(holdings_df: pd.DataFrame, title: str):
    """Displays ETF or Fund holdings in a table."""
    print(f"\n--- {title} ---")
    if not holdings_df.empty:
        # Limit columns if necessary, or ensure formatting is okay
        # Example: Limit to first 5 columns if too wide
        # display_df = holdings_df.iloc[:, :5]
        print(tabulate(holdings_df, headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print(f"No {title} found.")
