import pandas as pd
import numpy as np
import talib
from typing import Dict

def calculate_atr(data: pd.DataFrame, num_days: int = 14) -> pd.DataFrame:
    """
    Calculates Average True Range (ATR) and related metrics.
    Adds 'atr', 'atr_prev', 'ratr', 'price_change_atr' columns to the DataFrame.
    """
    if data.empty or not all(col in data.columns for col in ['high_price', 'low_price', 'close_price']):
        print("Warning: Insufficient data for ATR calculation.")
        # Add empty columns if they don't exist to prevent errors downstream
        for col in ['atr', 'atr_prev', 'ratr', 'price_change_atr']:
             if col not in data.columns: data[col] = np.nan
        return data

    try:
        # Calculate the ATR for each day
        high_low_range = data['high_price'] - data['low_price']
        high_close_range = abs(data['high_price'] - data['close_price'].shift())
        low_close_range = abs(data['low_price'] - data['close_price'].shift())

        # Ensure all components are Series before concat
        tr_components = [
            high_low_range.rename('hl'),
            high_close_range.rename('hc'),
            low_close_range.rename('lc')
        ]
        true_range_df = pd.concat(tr_components, axis=1)
        true_range = true_range_df.max(axis=1, skipna=False) # Don't skip NaNs initially

        # Calculate ATR using rolling mean - TALib's ATR is often preferred
        # data['atr'] = true_range.rolling(window=num_days).mean() # Simple mean ATR
        data['atr'] = talib.ATR(data['high_price'], data['low_price'], data['close_price'], timeperiod=num_days)


        # Calculate the previous day's ATR and the RATR for each day
        data['atr_prev'] = data['atr'].shift()
        # Avoid division by zero or NaN
        data['ratr'] = (data['atr'] / data['atr_prev']).replace([np.inf, -np.inf], np.nan)


        # Calculate the daily price change in ATR units
        price_change = data['close_price'] - data['close_price'].shift()
        # Avoid division by zero or NaN
        price_change_atr = (price_change / data['atr']).replace([np.inf, -np.inf], np.nan)
        data['price_change_atr'] = round(np.abs(price_change_atr), 2) # Absolute and rounded

    except Exception as e:
        print(f"Error calculating ATR: {e}")
        # Ensure columns exist even if calculation fails
        for col in ['atr', 'atr_prev', 'ratr', 'price_change_atr']:
             if col not in data.columns: data[col] = np.nan

    return data


def calculate_rsi(data: pd.DataFrame, timeperiod: int = 14) -> pd.Series:
    """Calculates the Relative Strength Index (RSI)."""
    if data.empty or 'close_price' not in data.columns:
        print("Warning: Insufficient data for RSI calculation.")
        return pd.Series(dtype=float)
    try:
        rsi = talib.RSI(data['close_price'], timeperiod=timeperiod)
        return rsi
    except Exception as e:
        print(f"Error calculating RSI: {e}")
        return pd.Series(dtype=float)


def calculate_atr_stop_value(data: pd.DataFrame, timeperiod: int = 14) -> float:
    """Calculates the latest ATR value (not the stop price itself)."""
    if data.empty or not all(col in data.columns for col in ['high_price', 'low_price', 'close_price']):
        print("Warning: Insufficient data for ATR Stop calculation.")
        return 0.0 # Return 0 if data is insufficient

    try:
        # Ensure inputs are float64, handle potential NaNs before passing to talib
        high = data['high_price'].astype(np.float64).ffill()
        low = data['low_price'].astype(np.float64).ffill()
        close = data['close_price'].astype(np.float64).ffill()

        if high.isna().any() or low.isna().any() or close.isna().any():
             print("Warning: NaNs detected in price data after ffill for ATR calculation.")
             # Decide handling: return 0, raise error, or proceed with potential talib issues
             return 0.0


        atr_series = talib.ATR(high, low, close, timeperiod=timeperiod)
        latest_atr = atr_series.iloc[-1]
        return round(latest_atr, 2) if pd.notna(latest_atr) else 0.0
    except Exception as e:
        print(f"Error calculating ATR for stop value: {e}")
        return 0.0 # Return 0 on error


def calculate_rvol(data: pd.DataFrame, timeperiod: int = 14) -> pd.DataFrame:
    """
    Calculates Relative Volume (RVOL).
    Adds 'avg_volume' and 'rvol' columns to the DataFrame.
    """
    if data.empty or 'volume' not in data.columns:
        print("Warning: Insufficient data for RVOL calculation.")
        if 'avg_volume' not in data.columns: data['avg_volume'] = np.nan
        if 'rvol' not in data.columns: data['rvol'] = np.nan
        return data

    try:
        # Calculate the average volume
        data['avg_volume'] = data['volume'].rolling(window=timeperiod, min_periods=1).mean()

        # Calculate the RVOL for each day, handle division by zero/NaN
        data['rvol'] = (data['volume'] / data['avg_volume']).replace([np.inf, -np.inf], np.nan)
        data['rvol'] = round(data['rvol'], 2)

    except Exception as e:
        print(f"Error calculating RVOL: {e}")
        if 'avg_volume' not in data.columns: data['avg_volume'] = np.nan
        if 'rvol' not in data.columns: data['rvol'] = np.nan

    return data


def calculate_mas(data: pd.DataFrame) -> pd.DataFrame:
    """
    Calculates standard moving averages (EMA8, EMA20, SMA50, SMA200)
    and checks if the close price is above them.
    Adds MA columns and boolean 'is_above' columns.
    """
    if data.empty or 'close_price' not in data.columns:
        print("Warning: Insufficient data for MA calculation.")
        # Add empty columns if they don't exist
        for col in ['ema8', 'ema20', 'sma50', 'sma200', 'is_above_ema8', 'is_above_ema20', 'is_above_sma50', 'is_above_sma200']:
             if col not in data.columns: data[col] = np.nan if 'sma' in col or 'ema' in col else False # Booleans default False
        return data

    try:
        # Calculate EMAs and SMAs
        data['ema8'] = data['close_price'].ewm(span=8, adjust=False).mean()
        data['ema20'] = data['close_price'].ewm(span=20, adjust=False).mean()
        data['sma50'] = data['close_price'].rolling(window=50, min_periods=1).mean() # Use min_periods
        data['sma200'] = data['close_price'].rolling(window=200, min_periods=1).mean() # Use min_periods

        # Check if the latest close price is above or below each EMA/SMA
        data['is_above_ema8'] = data['close_price'] > data['ema8']
        data['is_above_ema20'] = data['close_price'] > data['ema20']
        data['is_above_sma50'] = data['close_price'] > data['sma50']
        data['is_above_sma200'] = data['close_price'] > data['sma200']

    except Exception as e:
        print(f"Error calculating MAs: {e}")
        # Ensure columns exist even if calculation fails
        for col in ['ema8', 'ema20', 'sma50', 'sma200', 'is_above_ema8', 'is_above_ema20', 'is_above_sma50', 'is_above_sma200']:
             if col not in data.columns: data[col] = np.nan if 'sma' in col or 'ema' in col else False

    return data

def calculate_atr_percentage_and_sma_gain(data: pd.DataFrame, atr_period: int = 14, sma_period: int = 50) -> Dict[str, float]:
    """
    Calculates ATR%, % Gain From SMA, and ATR% Multiple from SMA.
    Assumes 'atr' and 'sma<period>' columns are already calculated (e.g., by calculate_atr and calculate_mas).
    """
    results = {
        'atr_percentage': 0.0,
        'gain_from_sma': 0.0,
        'atr_multiple_from_sma': 0.0
    }
    sma_col = f'sma{sma_period}'
    if data.empty or not all(col in data.columns for col in ['close_price', 'atr', sma_col]):
        print(f"Warning: Insufficient data for ATR%/SMA Gain calculation (requires close_price, atr, {sma_col}).")
        return results

    try:
        latest_close_price = data['close_price'].iloc[-1]
        latest_atr = data['atr'].iloc[-1] if pd.notna(data['atr'].iloc[-1]) else 0
        latest_sma = data[sma_col].iloc[-1] if pd.notna(data[sma_col].iloc[-1]) else 0

        # Calculations (handle potential division by zero)
        results['atr_percentage'] = (latest_atr / latest_close_price) * 100 if latest_close_price != 0 else 0
        results['gain_from_sma'] = ((latest_close_price - latest_sma) / latest_sma) * 100 if latest_sma != 0 else 0
        results['atr_multiple_from_sma'] = results['gain_from_sma'] / results['atr_percentage'] if results['atr_percentage'] != 0 else 0

    except Exception as e:
        print(f"Error calculating ATR%/SMA Gain: {e}")
        # Return default results on error

    return results
