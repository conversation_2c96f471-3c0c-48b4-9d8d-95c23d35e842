"""
Bond Analysis Runner

This script runs the enhanced bond analysis and visualization tools.
"""

import os
import sys
import argparse
from datetime import datetime

def main():
    parser = argparse.ArgumentParser(description='Run bond analysis tools')
    parser.add_argument('--file', type=str, help='Path to the bond CSV file')
    parser.add_argument('--analysis-only', action='store_true', help='Run only the analysis, not visualization')
    parser.add_argument('--viz-only', action='store_true', help='Run only the visualization on most recent analysis')
    parser.add_argument('--max-spread', type=float, default=2.0, help='Maximum acceptable bid-ask spread')
    parser.add_argument('--min-quantity', type=int, default=10, help='Minimum quantity available')
    parser.add_argument('--min-ask-quantity', type=int, default=0, help='Minimum required purchase quantity (the number in parentheses)')
    parser.add_argument('--max-ask-quantity', type=int, default=1000000, help='Maximum required purchase quantity (the number in parentheses)')
    parser.add_argument('--min-yield', type=float, default=2.5, help='Minimum yield to worst')
    parser.add_argument('--min-rating', type=int, default=5, help='Minimum rating score (0-10)')
    parser.add_argument('--callable', choices=['all', 'callable', 'non-callable'], default='all', help='Filter for callable bonds')
    parser.add_argument('--call-protection', choices=['all', 'protected', 'unprotected'], default='all', help='Filter for bonds with call protection')
    parser.add_argument('--tax-state', type=str, default='MA', help='State code for tax calculations (default: MA)')
    parser.add_argument('--federal-tax-rate', type=float, default=0.22, help='Federal tax rate (default: 0.22)')
    parser.add_argument('--state-tax-rate', type=float, default=0.05, help='State tax rate (default: 0.05 for MA)')

    args = parser.parse_args()

    # Get the current script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Add the parent directory to sys.path
    parent_dir = os.path.dirname(script_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Default file path if not provided
    if not args.file:
        # Use a relative path in the data directory
        data_dir = os.path.join(os.path.dirname(os.path.dirname(script_dir)), 'data', 'bonds')
        os.makedirs(data_dir, exist_ok=True)
        args.file = os.path.join(data_dir, 'Fidelity_FixedIncome_SearchResults.csv')

    # Run analysis if not viz-only
    if not args.viz_only:
        print(f"Running enhanced bond analysis on {args.file}...")
        print(f"Parameters: max_spread={args.max_spread}, min_quantity={args.min_quantity}, min_ask_quantity={args.min_ask_quantity}, max_ask_quantity={args.max_ask_quantity}, min_yield={args.min_yield}, min_rating={args.min_rating}, callable={args.callable}, call_protection={args.call_protection}, tax_state={args.tax_state}, federal_tax_rate={args.federal_tax_rate}, state_tax_rate={args.state_tax_rate}")

        # Modify the enhanced_analysis.py globals
        from bonds.enhanced_analysis import main as run_analysis
        import bonds.enhanced_analysis as ea

        # Set the parameters
        ea.MAX_SPREAD = args.max_spread
        ea.MIN_QUANTITY = args.min_quantity
        ea.MIN_ASK_QUANTITY = args.min_ask_quantity
        ea.MAX_ASK_QUANTITY = args.max_ask_quantity
        ea.MIN_YIELD = args.min_yield
        ea.MIN_RATING_SCORE = args.min_rating
        ea.CALLABLE_FILTER = args.callable
        ea.CALL_PROTECTION_FILTER = args.call_protection
        ea.TAX_STATE = args.tax_state
        ea.FEDERAL_TAX_RATE = args.federal_tax_rate
        ea.STATE_TAX_RATE = args.state_tax_rate

        # Run the analysis
        run_analysis()

    # Run visualization if not analysis-only
    if not args.analysis_only:
        print("Running bond visualization...")
        from bonds.visualization import main as run_visualization
        run_visualization()

    print("Bond analysis complete!")

if __name__ == "__main__":
    start_time = datetime.now()
    main()
    end_time = datetime.now()
    print(f"Total execution time: {end_time - start_time}")
