import os
import time
import requests
import docx
import datetime
import datetime
import time
import requests
from stockdata.data_source import getStockDataV3
import docx
import os

headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36'}

def download_image(url, file_path):
    try:
        with open(file_path, 'wb') as handle:
            response = requests.get(url, headers=headers)
            if not response.ok:
                print(response)
            for block in response.iter_content(1024):
                if not block:
                    break
                handle.write(block)
        return True
    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return False

def add_image_to_doc(doc, file_path):
    try:
        doc.add_picture(file_path)
    except Exception as e:
        print(f"Error adding image {file_path} to document: {e}")
    finally:
        os.remove(file_path)

def create_vertical_table(doc, sector, industry, sub_industry, forward_eps_growth,
                           eps_growth_last_qtr, eps_growth_ttm_vs_prior_ttm, eps_growth_3_year, eps_growth_5_year,
                           eps_growth_proj_next_yr_vs_this_yr, revenue_growth_ttm_vs_prior_ttm, revenue_growth_last_qtr,
                           equity_summary_score):
    # Create a vertical table
    table = doc.add_table(rows=14, cols=2)
    table.autofit = True

    # Define the data to be added to the table
    table_data = [
        #('Ticker:', f'{ticker}'),
        ('Sector:', f'{sector}'),
        ('Industry:', f'{industry}'),
        ('Sub-Industry:', f'{sub_industry}'),
        ('Forward EPS Long Term Growth:', f'{forward_eps_growth}'),
        ('EPS Growth Last Qtr:', f'{eps_growth_last_qtr}'),
        ('EPS Growth TTM vs Prior TTM:', f'{eps_growth_ttm_vs_prior_ttm}'),
        ('EPS Growth 3 Year:', f'{eps_growth_3_year}'),
        ('EPS Growth 5 Year:', f'{eps_growth_5_year}'),
        ('EPS Growth Proj Next Yr vs This Yr:', f'{eps_growth_proj_next_yr_vs_this_yr}'),
        ('Revenue Growth TTM vs Prior TTM:', f'{revenue_growth_ttm_vs_prior_ttm}'),
        ('Revenue Growth Last Qtr:', f'{revenue_growth_last_qtr}'),
        ('Equity Summary Score:', f'{equity_summary_score}')
    ]

    # Add data to the table using a loop
    for row_idx, (label, value) in enumerate(table_data):
        table.cell(row_idx, 0).text = label
        table.cell(row_idx, 1).text = value

    return table

def process_ticker(row, doc, save_doc,daily_id, wkly_id):
    xyz = row['Symbol']
    df = getStockDataV3(xyz)

    if (df is not None
        and df.size > 0
        and df['slope_50'][0] > 0
        and df['slope_200'][0] > 0
        and df['UDRatio'][0] > 0.75
        and df['150'][0] > df['200'][0]
        and df['50'][0] > df['150'][0]
        and df['50'][0] > df['200'][0]
        and df['$VolumeM'][0] >= 25
        and df['RS_Rating'][0] > 77
    ):
        if save_doc:

         base_url_daily = f'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i={daily_id}&r=tdy'
         base_url_weekly = f'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=5&mn=0&dy=0&i={wkly_id}&r=tdy'

         daily_url = base_url_daily.replace('tdy', str(time.time())).replace('ticker', xyz)
         daily_image_path = f'src/data/images{xyz}.png'

         if download_image(daily_url, daily_image_path):
             add_image_to_doc(doc, daily_image_path)

         weekly_url = base_url_weekly.replace('tdy', str(time.time())).replace('ticker', xyz)
         weekly_image_path = f'src/data/images{xyz}_week.png'

         if download_image(weekly_url, weekly_image_path):
             add_image_to_doc(doc, weekly_image_path)


             create_vertical_table(
doc,
row['Sector'],
row['Industry'],
row['Sub-Industry'],
#row['Beta (1 Year Annualized)'],
#row['Standard Deviation (1 Yr Annualized)'],
row['Forward EPS Long Term Growth (3-5 Yrs)'],
row['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'],
row['EPS Growth (TTM vs Prior TTM)'],
row['EPS Growth (3 Year History)'],
row['EPS Growth (5 Year Historical)'],
row['EPS Growth (Proj Next Yr vs. This Yr)'],
row['Revenue Growth (TTM vs. Prior TTM)'],
row['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'],
row['Equity Summary Score (ESS) from LSEG StarMine'],
round(df['ADRP'][0], 2))

    else:
        return xyz, determine_exclusion_reasons(df) , df
    return None, None , df

def determine_exclusion_reasons(df):
    reasons = []
    if df is None:
       reasons.append("DataFrame is None")
       return
    if df.size == 0:
        reasons.append("DataFrame is empty")
    if df['slope_50'][0] <= 0:
        reasons.append("slope_50 not > 0")
    if df['slope_200'][0] <= 0:
        reasons.append("slope_200 not > 0")
    if df['UDRatio'][0] <= 0.75:
        reasons.append("UDRatio not > 0.75")
    if not (df['150'][0] > df['200'][0]):
        reasons.append("150 not > 200")
    if not (df['50'][0] > df['150'][0]):
        reasons.append("50 not > 150")
    if not (df['50'][0] > df['200'][0]):
        reasons.append("50 not > 200")
    if df['$VolumeM'][0] < 25:
        reasons.append("$VolumeM not > 25")
    if df['RS_Rating'][0] <= 77:
        reasons.append("RS_Rating not > 77")
    return ', '.join(reasons)

def save_filtered_screener(screenerdf, excluded_tickers):
    excluded_symbols = [ticker for ticker, _ in excluded_tickers]
    df = screenerdf[~screenerdf['Symbol'].isin(excluded_symbols)] # # Create a filtered copy of the DataFrame to avoid modifying a slice    df = screenerdf.loc[~screenerdf['Symbol'].isin(excluded_symbols)].copy()

    # Calculate the size of each group
    df['Sector_Count'] = df.groupby('Sector')['Percentile'].transform('size')
    df['Industry_Count'] = df.groupby(['Sector', 'Industry'])['Percentile'].transform('size')
    df['SubIndustry_Count'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].transform('size')

    # Sort by 'Sector_Count', 'Industry_Count', 'SubIndustry_Count' in descending order
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], ascending=[False, False, False])

    # Rank based on 'Percentile' within each group defined by 'Sector', 'Industry', 'Sub-Industry'
    df['Rank'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].rank(ascending=False)

    # Final sort by the calculated 'Rank'
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count', 'Rank'], ascending=[False, False, False, True])

    # Drop auxiliary columns if not needed
    df.drop(columns=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], inplace=True)

    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    file_name = f"filtered_sorted_screener_{current_date}.xlsx"
    df.to_excel(file_name)


def downloadChartsFidelityV2(screenerdf, docName,save_doc=False):

    doc = None
    daily_id = None
    wkly_id = None
    if save_doc:
     doc = docx.Document()
     daily_id = input("Enter daily id: ")
     wkly_id = input("Enter weekly id: ")
    fileName = f'{docName}.docx' if docName else 'images.docx'
    excluded_tickers = []

    for _, row in screenerdf.iterrows():
     # Process ticker and get details
     ticker, reason, df = process_ticker(row, doc, save_doc,daily_id, wkly_id)

     # Handle excluded tickers
     if ticker:
         excluded_tickers.append((ticker, reason))
     else:
         # Check and add 'TTM' to screenerdf
         if not df.empty and 'TTM' in df.columns:
             try:
                 # Assuming each row in screenerdf can be matched to df by 'Symbol'
                 screenerdf.at[row.name, 'TTM'] = df['TTM'].values[0]  # Use .values[0] if a single value
             except (KeyError, IndexError):
                 print(f"Issue adding TTM for {row['Symbol']}")
         if not df.empty and 'RS_Rating' in df.columns:
             try:
                 # Assuming each row in screenerdf can be matched to df by 'Symbol'
                 screenerdf.at[row.name, 'Percentile'] = df['RS_Rating'].values[0]  # Use .values[0] if a single value
             except (KeyError, IndexError):
                 print(f"Issue adding TTM for {row['Symbol']}")



    if excluded_tickers:
        for ticker, reason in excluded_tickers:
            print(f"Excluded {ticker} for reasons: {reason}")

    save_filtered_screener(screenerdf, excluded_tickers)

    if save_doc:
        doc.save(fileName)
        os.startfile(fileName)

def filter_stocks(screenerdf):
    """
    Filter stocks based on technical criteria and return filtered dataframe and excluded tickers
    """
    excluded_tickers = []

    for _, row in screenerdf.iterrows():
        # Get the stock data
        xyz = row['Symbol']
        df = getStockDataV3(xyz)

        # Check if stock meets technical criteria
        if (df is not None
            and df.size > 0
            and df['slope_50'][0] > 0
            and df['slope_200'][0] > 0
            and df['UDRatio'][0] > 0.75
            and df['150'][0] > df['200'][0]
            and df['50'][0] > df['150'][0]
            and df['50'][0] > df['200'][0]
            and df['$VolumeM'][0] >= 25
            and df['RS_Rating'][0] > 77
        ):
            # Update screenerdf with additional metrics if stock passes filters
            if not df.empty:
                if 'TTM' in df.columns:
                    try:
                        screenerdf.at[row.name, 'TTM'] = df['TTM'].values[0]
                    except (KeyError, IndexError):
                        print(f"Issue adding TTM for {xyz}")
                if 'RS_Rating' in df.columns:
                    try:
                        screenerdf.at[row.name, 'Percentile'] = df['RS_Rating'].values[0]
                    except (KeyError, IndexError):
                        print(f"Issue adding RS_Rating for {xyz}")
        else:
            # Add to excluded tickers if stock doesn't meet criteria
            reason = determine_exclusion_reasons(df)
            excluded_tickers.append((xyz, reason))

    return screenerdf, excluded_tickers

def download_stock_charts(screenerdf, doc_name=None, daily_id=None, wkly_id=None):
    """
    Download charts for filtered stocks and create document
    """
    if  doc_name is None or daily_id is None or wkly_id is None:
        print("Missing required parameters: doc_name, daily_id, or wkly_id")
        return

    doc = docx.Document()
    file_name = f'{doc_name}.docx'

    for _, row in screenerdf.iterrows():
        xyz = row['Symbol']

        # Download daily chart
        base_url_daily = f'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i={daily_id}&r=tdy'
        daily_url = base_url_daily.replace('tdy', str(time.time())).replace('ticker', xyz)
        daily_image_path = f'src/data/images{xyz}.png'

        if download_image(daily_url, daily_image_path):
            add_image_to_doc(doc, daily_image_path)

        # Download weekly chart
        base_url_weekly = f'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=2&mn=6&dy=0&i={wkly_id}&r=tdy'
        weekly_url = base_url_weekly.replace('tdy', str(time.time())).replace('ticker', xyz)
        weekly_image_path = f'src/data/images{xyz}_week.png'

        if download_image(weekly_url, weekly_image_path):
            add_image_to_doc(doc, weekly_image_path)

        # Add stock details table
        create_vertical_table(
            doc,
            row['Sector'],
            row['Industry'],
            row['Sub-Industry'],
            row['Forward EPS Long Term Growth (3-5 Yrs)'],
            row['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'],
            row['EPS Growth (TTM vs Prior TTM)'],
            row['EPS Growth (3 Year History)'],
            row['EPS Growth (5 Year Historical)'],
            row['EPS Growth (Proj Next Yr vs. This Yr)'],
            row['Revenue Growth (TTM vs. Prior TTM)'],
            row['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'],
            row['Equity Summary Score (ESS) from LSEG StarMine'],
            #round(getStockDataV3(xyz)['ADRP'][0], 2)
        )

        print("Processed chart for stock:",xyz)

    doc.save(file_name)
    os.startfile(file_name)

def downloadChartsFidelityV2(screenerdf, doc_name=None):
    """
    Main function that coordinates filtering and chart download
    """
    # Step 1: Filter stocks
    filtered_df, excluded_tickers = filter_stocks(screenerdf)

    # Step 2: Print excluded tickers
    if excluded_tickers:
        for ticker, reason in excluded_tickers:
            print(f"Excluded {ticker} for reasons: {reason}")

    # Step 3: Save filtered screener
    save_filtered_screener(filtered_df, excluded_tickers)

    # Step 4: Download charts if document name is provided
    if doc_name:
        daily_id = input("Enter daily id: ")
        wkly_id = input("Enter weekly id: ")
        download_stock_charts(filtered_df, doc_name, daily_id, wkly_id)
