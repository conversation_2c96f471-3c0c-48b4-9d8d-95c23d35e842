import asyncio
import aiohttp
import json
from typing import List, Dict, Set
from fidelity.common.headers import get_fidelity_headers_v2
import pandas as pd
from datetime import datetime
from fidelity.scanner.screener_request import intra_screen
from dotenv import load_dotenv
import os

load_dotenv()

# --- Configuration ---
FIDELITY_FILE = os.getenv("FIDELITY_FILE")
MAX_SYMBOLS_PER_REQUEST = 50
URL = "https://digital.fidelity.com/prgw/digital/research/api/quote"

def get_csv_stock_list_from_excel(file_path: str) -> List[str]:
    """Reads stock symbols from an Excel file, filtering by a TTM column."""
    try:
        df = pd.read_excel(file_path, engine='openpyxl')
        if 'Symbol' not in df.columns or 'TTM' not in df.columns:
            raise ValueError("Excel file must contain 'Symbol' and 'TTM' columns.")
        # df = df[df['TTM'] == True] # This filter can be enabled if needed
        return df['Symbol'].astype(str).tolist()
    except FileNotFoundError:
        print(f"Error: The file was not found at {file_path}")
        return []
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return []

def chunk_list(data: List[str], chunk_size: int) -> List[List[str]]:
    """Splits a list into smaller chunks."""
    return [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]

def get_volume_percentage_threshold(current_time: datetime) -> float:
    """Determines the relative volume (rvol) threshold based on the time of day."""
    hour, minute = current_time.hour, current_time.minute
    if (hour == 9 and minute >= 30) or (hour == 10 and minute < 30):
        return 25
    elif (hour == 10 and minute >= 30) or (hour == 11) or (hour == 12 and minute < 30):
        return 40
    elif (hour == 12 and minute >= 30) or (hour == 13) or (hour == 14 and minute < 30):
        return 60
    elif (hour == 14 and minute >= 30) or (hour == 15):
        return 80
    return 100

def filter_and_sort_symbols(response_data: Dict, volume_percentage: float) -> List[str]:
    """Filters and sorts symbols from the API response based on volume criteria."""
    qualified_symbols = []
    if not isinstance(response_data, dict) or 'quoteData' not in response_data:
        return []

    for data in response_data['quoteData']:
        try:
            if not isinstance(data, dict):
                continue

            pct_chg_today = float(data.get('pctChgToday', '0').strip('%')) / 100
            volume = float(data.get('volume', '0').replace(',', ''))
            avg_vol_10_day = float(data.get('avgVol10Day', '0').replace(',', ''))

            if avg_vol_10_day > 0 and pct_chg_today > 0 and (volume / avg_vol_10_day) * 100 >= volume_percentage:
                ratio = volume / avg_vol_10_day
                qualified_symbols.append((data['symbol'], ratio))
        except (ValueError, AttributeError, TypeError):
            continue

    qualified_symbols.sort(key=lambda x: x[1], reverse=True)
    return [symbol for symbol, _ in qualified_symbols]

async def fetch_chunk(session: aiohttp.ClientSession, symbols: List[str], volume_percentage: float) -> List[str]:
    """Asynchronously fetches and processes a single chunk of symbols."""
    payload = json.dumps({"symbol": ",".join(symbols)})
    headers = get_fidelity_headers_v2()

    try:
        async with session.post(URL, headers=headers, data=payload) as response:
            response.raise_for_status()
            response_data = await response.json()
            return filter_and_sort_symbols(response_data, volume_percentage)
    except aiohttp.ClientError as e:
        print(f"Error fetching chunk {symbols[0]}...: {e}")
        return []
    except json.JSONDecodeError:
        print(f"Error decoding JSON for chunk {symbols[0]}...")
        return []

async def main():
    """Main asynchronous function to run the screener."""
    if not FIDELITY_FILE:
        print("Error: FIDELITY_FILE environment variable is not set.")
        return

    print(f"Running scan on file: {FIDELITY_FILE}")

    symbol_list = get_csv_stock_list_from_excel(FIDELITY_FILE)
    if not symbol_list:
        print("No symbols to process.")
        return

    symbol_chunks = chunk_list(symbol_list, MAX_SYMBOLS_PER_REQUEST)
    current_time = datetime.now()
    volume_percentage_threshold = get_volume_percentage_threshold(current_time)

    print(f"Current Time: {current_time:%Y-%m-%d %H:%M:%S}, Volume Threshold: {volume_percentage_threshold}%")

    all_symbols: Set[str] = set()

    async with aiohttp.ClientSession() as session:
        # Create tasks for fetching symbol chunks and for the intra_screen
        quote_tasks = [fetch_chunk(session, chunk, volume_percentage_threshold) for chunk in symbol_chunks]
        intra_screen_task = intra_screen(session, 1 + (volume_percentage_threshold / 100))

        # Gather results from all tasks
        results = await asyncio.gather(*quote_tasks, intra_screen_task)

        # Process the results
        for result in results:
            if result:
                all_symbols.update(result)

    # Convert set to list for final output
    final_symbols = list(all_symbols)
    print("\nQualified Symbols:", final_symbols)

    # Split into comma-separated strings for output
    output_chunks = [",".join(final_symbols[i:i+30]) for i in range(0, len(final_symbols), 30)]
    print("\nFormatted Output Chunks:")
    for chunk in output_chunks:
        print(chunk)

if __name__ == "__main__":
    asyncio.run(main())
