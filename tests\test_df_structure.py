"""
Test script to examine the DataFrame structure in getStockDataV3.
"""

import sys
import os

# Add src directory to Python path
sys.path.append('src')

from stockdata.data_source import getStockDataV3
import pandas as pd

def test_df_structure():
    """Test the DataFrame structure in getStockDataV3."""
    print("Testing DataFrame structure in getStockDataV3...")
    
    # Get stock data for Apple
    df = yf.download('AAPL', period='1mo', interval='1d', auto_adjust=False)
    
    # Print DataFrame structure
    print(f"DataFrame shape: {df.shape}")
    print(f"DataFrame index: {df.index}")
    print(f"DataFrame columns: {df.columns}")
    print(f"DataFrame index type: {type(df.index)}")
    print(f"DataFrame column type: {type(df.columns)}")
    
    # Print first few rows
    print("\nFirst 5 rows:")
    print(df.head())
    
    # Check if the DataFrame has a MultiIndex
    if isinstance(df.index, pd.MultiIndex):
        print("\nDataFrame has a MultiIndex for index")
        print(f"Index levels: {df.index.names}")
    else:
        print("\nDataFrame has a regular Index for index")
        print(f"Index name: {df.index.name}")
    
    if isinstance(df.columns, pd.MultiIndex):
        print("\nDataFrame has a MultiIndex for columns")
        print(f"Column levels: {df.columns.names}")
    else:
        print("\nDataFrame has a regular Index for columns")
        print(f"Column names: {df.columns.tolist()}")

if __name__ == "__main__":
    import yfinance as yf
    test_df_structure()
