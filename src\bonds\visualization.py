"""
Bond Data Visualization

This script creates visualizations of bond data to help analyze liquidity,
yield, and other important metrics.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import glob
from datetime import datetime

def load_most_recent_analysis():
    """Load the most recent bond analysis file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Find all CSV files matching the pattern
    csv_files = glob.glob(os.path.join(script_dir, "liquid_bonds_*.csv"))

    if not csv_files:
        print("No analysis files found. Please run enhanced_analysis.py first.")
        return None

    # Get the most recent file
    most_recent = max(csv_files, key=os.path.getctime)
    print(f"Loading most recent analysis: {most_recent}")

    return pd.read_csv(most_recent)

def create_visualizations(df):
    """Create various visualizations of the bond data."""
    if df is None or len(df) == 0:
        print("No data to visualize.")
        return

    # Set the style
    sns.set_theme(style="whitegrid")

    # Create a directory for the visualizations
    script_dir = os.path.dirname(os.path.abspath(__file__))
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    viz_dir = os.path.join(script_dir, f"bond_viz_{timestamp}")
    os.makedirs(viz_dir, exist_ok=True)

    # 1. Scatter plot of Yield vs Spread with Liquidity Score as color
    plt.figure(figsize=(12, 8))
    x = np.array(df['Spread'])
    y = np.array(df['Ask Yield to Worst'])
    colors = np.array(df['Liquidity Score'])
    scatter = plt.scatter(
        x, y,
        c=colors,
        cmap='viridis',
        alpha=0.7,
        s=100
    )
    plt.colorbar(scatter, label='Liquidity Score')
    plt.xlabel('Bid-Ask Spread')
    plt.ylabel('Yield to Worst (%)')
    plt.title('Bond Yield vs Spread (Color = Liquidity Score)')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '1_yield_vs_spread.png'))
    plt.close()

    # 2. Scatter plot of Yield vs Maturity with Rating as color
    plt.figure(figsize=(12, 8))
    x = np.array(df['Years to Maturity'])
    y = np.array(df['Ask Yield to Worst'])
    colors = np.array(df['Rating_Score'])
    scatter = plt.scatter(
        x, y,
        c=colors,
        cmap='RdYlGn',
        alpha=0.7,
        s=100
    )
    plt.colorbar(scatter, label='Rating Score (Higher = Better)')
    plt.xlabel('Years to Maturity')
    plt.ylabel('Yield to Worst (%)')
    plt.title('Bond Yield vs Maturity (Color = Rating)')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '2_yield_vs_maturity.png'))
    plt.close()

    # 3. Histogram of Liquidity Scores
    plt.figure(figsize=(12, 6))
    # Convert to numpy array to avoid pandas multi-dimensional indexing issue
    liquidity_scores = np.array(df['Liquidity Score'])
    # Use matplotlib's histogram directly instead of seaborn
    plt.hist(liquidity_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    # Add a density curve
    from scipy import stats
    density = stats.gaussian_kde(liquidity_scores)
    x_vals = np.linspace(min(liquidity_scores), max(liquidity_scores), 100)
    plt.plot(x_vals, density(x_vals) * len(liquidity_scores) * (max(liquidity_scores) - min(liquidity_scores)) / 20,
             color='red', linewidth=2)
    plt.xlabel('Liquidity Score')
    plt.ylabel('Count')
    plt.title('Distribution of Bond Liquidity Scores')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '3_liquidity_score_distribution.png'))
    plt.close()

    # 4. Bar chart of average yield by rating
    plt.figure(figsize=(12, 6))
    rating_yield = df.groupby('Rating_Score')['Ask Yield to Worst'].mean().reset_index()
    rating_yield = rating_yield.sort_values('Rating_Score')
    # Use x and y arrays directly to avoid pandas indexing issues
    x = np.array(rating_yield['Rating_Score'])
    y = np.array(rating_yield['Ask Yield to Worst'])
    plt.bar(x, y)
    plt.xlabel('Rating Score (Higher = Better)')
    plt.ylabel('Average Yield to Worst (%)')
    plt.title('Average Yield by Rating')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '4_yield_by_rating.png'))
    plt.close()

    # 5. Heatmap of correlation between key metrics
    plt.figure(figsize=(12, 10))
    corr_columns = ['Spread', 'Liquidity Score', 'Ask Yield to Worst',
                   'Years to Maturity', 'Rating_Score', 'Quantity_Available',
                   'Current Yield (%)', 'Total Return (%)']
    # Calculate correlation matrix
    corr_matrix = np.zeros((len(corr_columns), len(corr_columns)))
    for i, col1 in enumerate(corr_columns):
        for j, col2 in enumerate(corr_columns):
            # Convert to numpy arrays
            data1 = np.array(df[col1])
            data2 = np.array(df[col2])
            # Calculate correlation
            corr_matrix[i, j] = np.corrcoef(data1, data2)[0, 1]

    # Create a simple heatmap using imshow
    plt.imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    plt.colorbar(label='Correlation')

    # Add annotations
    for i in range(len(corr_columns)):
        for j in range(len(corr_columns)):
            plt.text(j, i, f'{corr_matrix[i, j]:.2f}',
                     ha='center', va='center', color='black')

    # Add labels
    plt.xticks(range(len(corr_columns)), corr_columns, rotation=45, ha='right')
    plt.yticks(range(len(corr_columns)), corr_columns)
    plt.title('Correlation Between Bond Metrics')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '5_correlation_heatmap.png'))
    plt.close()

    # 6. Scatter plot of Liquidity Score vs Quantity Available
    plt.figure(figsize=(12, 8))
    x = np.array(df['Quantity_Available'])
    y = np.array(df['Liquidity Score'])
    plt.scatter(x, y, alpha=0.7, s=100)
    plt.xlabel('Quantity Available')
    plt.ylabel('Liquidity Score')
    plt.title('Bond Liquidity Score vs Quantity Available')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '6_liquidity_vs_quantity.png'))
    plt.close()

    # 7. Top 10 most liquid bonds
    plt.figure(figsize=(14, 8))
    top_liquid = df.sort_values('Liquidity Score', ascending=False).head(10)
    x = np.array(top_liquid['Liquidity Score'])
    y = np.array(top_liquid['Description'])
    # Use horizontal bar plot
    plt.barh(y, x)
    plt.xlabel('Liquidity Score')
    plt.ylabel('Bond')
    plt.title('Top 10 Most Liquid Bonds')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '7_top_liquid_bonds.png'))
    plt.close()

    # 8. Top 10 highest yield bonds
    plt.figure(figsize=(14, 8))
    top_yield = df.sort_values('Ask Yield to Worst', ascending=False).head(10)
    x = np.array(top_yield['Ask Yield to Worst'])
    y = np.array(top_yield['Description'])
    # Use horizontal bar plot
    plt.barh(y, x)
    plt.xlabel('Yield to Worst (%)')
    plt.ylabel('Bond')
    plt.title('Top 10 Highest Yield Bonds')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, '8_top_yield_bonds.png'))
    plt.close()

    print(f"Visualizations created in directory: {viz_dir}")

    # Create an HTML report with all visualizations
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Bond Analysis Report - {timestamp}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2 {{ color: #2c3e50; }}
            .viz-container {{ margin-bottom: 30px; }}
            img {{ max-width: 100%; border: 1px solid #ddd; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }}
            th {{ background-color: #f2f2f2; }}
            tr:hover {{ background-color: #f5f5f5; }}
        </style>
    </head>
    <body>
        <h1>Bond Analysis Report</h1>
        <p>Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>

        <h2>Summary Statistics</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Total Bonds Analyzed</td>
                <td>{len(df)}</td>
            </tr>
            <tr>
                <td>Average Yield to Worst</td>
                <td>{df['Ask Yield to Worst'].mean():.2f}%</td>
            </tr>
            <tr>
                <td>Average Spread</td>
                <td>{df['Spread'].mean():.2f}</td>
            </tr>
            <tr>
                <td>Average Liquidity Score</td>
                <td>{df['Liquidity Score'].mean():.2f}</td>
            </tr>
            <tr>
                <td>Highest Yield to Worst</td>
                <td>{df['Ask Yield to Worst'].max():.2f}%</td>
            </tr>
            <tr>
                <td>Lowest Spread</td>
                <td>{df['Spread'].min():.2f}</td>
            </tr>
        </table>

        <h2>Visualizations</h2>

        <div class="viz-container">
            <h3>1. Bond Yield vs Spread</h3>
            <p>This scatter plot shows the relationship between bid-ask spread and yield, with color indicating liquidity score.</p>
            <img src="1_yield_vs_spread.png" alt="Yield vs Spread">
        </div>

        <div class="viz-container">
            <h3>2. Bond Yield vs Maturity</h3>
            <p>This scatter plot shows the relationship between years to maturity and yield, with color indicating rating score.</p>
            <img src="2_yield_vs_maturity.png" alt="Yield vs Maturity">
        </div>

        <div class="viz-container">
            <h3>3. Distribution of Liquidity Scores</h3>
            <p>This histogram shows the distribution of liquidity scores across all bonds.</p>
            <img src="3_liquidity_score_distribution.png" alt="Liquidity Score Distribution">
        </div>

        <div class="viz-container">
            <h3>4. Average Yield by Rating</h3>
            <p>This bar chart shows how average yield varies by credit rating.</p>
            <img src="4_yield_by_rating.png" alt="Yield by Rating">
        </div>

        <div class="viz-container">
            <h3>5. Correlation Between Bond Metrics</h3>
            <p>This heatmap shows the correlation between different bond metrics.</p>
            <img src="5_correlation_heatmap.png" alt="Correlation Heatmap">
        </div>

        <div class="viz-container">
            <h3>6. Liquidity Score vs Quantity Available</h3>
            <p>This scatter plot shows the relationship between quantity available and liquidity score.</p>
            <img src="6_liquidity_vs_quantity.png" alt="Liquidity vs Quantity">
        </div>

        <div class="viz-container">
            <h3>7. Top 10 Most Liquid Bonds</h3>
            <p>This bar chart shows the top 10 bonds by liquidity score.</p>
            <img src="7_top_liquid_bonds.png" alt="Top Liquid Bonds">
        </div>

        <div class="viz-container">
            <h3>8. Top 10 Highest Yield Bonds</h3>
            <p>This bar chart shows the top 10 bonds by yield to worst.</p>
            <img src="8_top_yield_bonds.png" alt="Top Yield Bonds">
        </div>
    </body>
    </html>
    """

    with open(os.path.join(viz_dir, 'bond_analysis_report.html'), 'w') as f:
        f.write(html_content)

    print(f"HTML report created: {os.path.join(viz_dir, 'bond_analysis_report.html')}")

def main():
    df = load_most_recent_analysis()
    if df is not None:
        create_visualizations(df)

if __name__ == "__main__":
    main()
