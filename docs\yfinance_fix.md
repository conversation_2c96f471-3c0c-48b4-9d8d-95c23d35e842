# Yahoo Finance API Fix

## Issue
When using the `yfinance` library, you may encounter issues with data retrieval, such as:
- "No data found for this date range, symbol may be delisted"
- Empty DataFrames being returned
- JSONDecodeError exceptions

## Solution
The issue is related to the User-Agent header that `yfinance` sends with its requests. Yahoo Finance API appears to be checking for specific user agent strings and rejecting requests with user agents it doesn't recognize or trust.

### Working User-Agent
The following User-Agent header has been confirmed to work:

```python
user_agent_headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2171.95 Safari/537.36'}
```

### How to Apply the Fix
1. Locate the `utils.py` file in your yfinance installation:
   ```
   <your_env>/Lib/site-packages/yfinance/utils.py
   ```

2. Update the `user_agent_headers` dictionary with the working User-Agent string.

3. If you're also using the `base.py` file directly, update any User-Agent headers there as well.

### Version Compatibility
This project uses `yfinance==0.1.83` which has been tested with this User-Agent fix.

#### Version History
- The User-Agent issue was fixed in yfinance version 0.2.54 (released in February 2025) via [Pull Request #2277](https://github.com/ranaroussi/yfinance/pull/2277).
- Versions prior to 0.2.54 may experience issues with Yahoo Finance API blocking requests.
- If you upgrade to version 0.2.54 or later, you may need to handle Series vs. scalar comparison issues (see below).

## Additional Notes
- Yahoo Finance API changes frequently, so this fix may need to be updated in the future.
- If you upgrade `yfinance`, you may need to reapply this fix.
- The specific Chrome version in the User-Agent string (`Chrome/42.0.2171.95`) seems to be important for compatibility.

## Related Issues
- Series vs. scalar comparison issues may occur with newer versions of `yfinance` (0.2.x+).
- The `auto_adjust` parameter default changed to `True` in newer versions, which may affect data structure.
