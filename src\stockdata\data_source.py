from util.rs_rating import RSCalculator
from util.util import loadMovingAverages, loadMovingAveragesV2
import yfinance as yf
import pandas as pd
# Removed: from pandas_datareader import data as pdr (not needed with newer yfinance)
import datetime
from yahoo_fin import stock_info as stkinfo
from common.http_session import get_session
import ssl
import asyncio
from functools import partial
import aiohttp

# Create a session with Chrome impersonation using our shared helper
session = get_session('src/certs/my_ca_bundle.pem')

async def _run_sync_in_executor(func, *args, **kwargs):
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))

import os

# Note: yf.pdr_override() was removed in yfinance 0.2.41+
# We now use yf.download() directly instead
# Use relative path for RS data
# Try both possible paths for RSRATING.csv
rs_data_path_1 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'rs_data', 'RSRATING.csv')
rs_data_path_2 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'data', 'rs_data', 'RSRATING.csv')
rs_data_path_3 = 'src/data/rs_data/RSRATING.csv'

# Check which path exists
if os.path.exists(rs_data_path_1):
    rs_data_path = rs_data_path_1
elif os.path.exists(rs_data_path_2):
    rs_data_path = rs_data_path_2
elif os.path.exists(rs_data_path_3):
    rs_data_path = rs_data_path_3
else:
    raise FileNotFoundError(f"Could not find RSRATING.csv in any of the expected locations: {rs_data_path_1}, {rs_data_path_2}, or {rs_data_path_3}")

print(f"Using RS data path: {rs_data_path}")
rs_calculator = RSCalculator(rs_data_path)


async def getStockDataV3(session: aiohttp.ClientSession, ticker: str):
    """
    Get stock data with technical indicators and RS Rating asynchronously.

    Args:
        ticker: Stock ticker symbol
    """
    periods = ["5y", "2y", "1y", "6mo", "3mo"]
    df = None
    used_period = None

    for period in periods:
        try:
            df = await _run_sync_in_executor(yf.download, ticker, period=period, interval='1d', auto_adjust=False)
        except TypeError as e:
            if "proxy" in str(e):
                print(f"Removing proxy parameter for compatibility with newer yfinance")
                df = await _run_sync_in_executor(yf.download, ticker, period=period, interval='1d', auto_adjust=False)
            else:
                raise
        if df is not None and not df.empty:
            used_period = period
            break

    if df is None or df.empty:
        return None

    if rs_calculator is not None and len(df) >= 63:
        rs_rating = rs_calculator.calculate_rs_rating(df, used_period)
        df['RS_Rating'] = rs_rating

    df = loadMovingAverages(df)
    return df


async def getStockDataV4(session: aiohttp.ClientSession, ticker):
    data = await _run_sync_in_executor(yf.Ticker, ticker)
    df = await _run_sync_in_executor(data.history, period="5y", interval='1mo', auto_adjust=False)
    df = loadMovingAverages(df)
    return df

async def getStockDetails(session: aiohttp.ClientSession, ticker):
    data = await _run_sync_in_executor(yf.Ticker, ticker)
    df = await _run_sync_in_executor(lambda: data.info) # Access .info attribute
    if df.get('sector') is not None and df.get('industry') is not None:
        return df['sector'], df['industry']
    else:
        return '',''

async def getStockDataV2(session: aiohttp.ClientSession, ticker, days):
    start = datetime.date.today() -datetime.timedelta(days=days)
    now=datetime.date.today()
    df = await _run_sync_in_executor(yf.download, ticker, start=start, end=now, progress=False, auto_adjust=False, session=session)
    df = loadMovingAveragesV2(df)
    return df

async def getStockData(session: aiohttp.ClientSession, ticker, period):
    data = await _run_sync_in_executor(yf.Ticker, ticker)
    df = await _run_sync_in_executor(data.history, period=period, auto_adjust=False)
    df = loadMovingAverages(df)
    return df

async def getBasicStockData(session: aiohttp.ClientSession, ticker):
    data = await _run_sync_in_executor(yf.Ticker, ticker)
    df = await _run_sync_in_executor(data.history, period="2y", auto_adjust=False)
    return df

def getPCRatioData():
    # Use relative path for PC Ratio data
    # Try both possible paths for PC_Ratio.csv
    pc_ratio_path_1 = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'PC_Ratio.csv')
    pc_ratio_path_2 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'data', 'PC_Ratio.csv')
    pc_ratio_path_3 = 'src/data/PC_Ratio.csv'

    # Check which path exists
    if os.path.exists(pc_ratio_path_1):
        pc_ratio_path = pc_ratio_path_1
    elif os.path.exists(pc_ratio_path_2):
        pc_ratio_path = pc_ratio_path_2
    elif os.path.exists(pc_ratio_path_3):
        pc_ratio_path = pc_ratio_path_3
    else:
        raise FileNotFoundError(f"Could not find PC_Ratio.csv in any of the expected locations: {pc_ratio_path_1}, {pc_ratio_path_2}, or {pc_ratio_path_3}")

    print(f"Using PC Ratio data path: {pc_ratio_path}")
    exisiting_df = pd.read_csv(pc_ratio_path, index_col=-1)
    exisiting_df['Date'] = pd.to_datetime(exisiting_df['Date'])
    exisiting_df.sort_values(by='Date', ascending=False, inplace=True)
    del exisiting_df['Date']
    exisiting_df.drop_duplicates(subset=None, keep='first', inplace=True)
    #print(exisiting_df.head())
    return exisiting_df['EQUITY PUT/CALL RATIO'].tolist()


#print(getStockData('SPY','20y').head(20))

#print(getStockDataV2('NVDA',90).head())

#print(getStockDetails('AMD'))


async def getQuoteTable(ticker):
    data = await _run_sync_in_executor(stkinfo.get_quote_table, ticker)
    range_str = data["52 Week Range"].split('-')
    high52 = range_str[1]
    low52 =  range_str[0]
    return pd.DataFrame({'Ticker':ticker,'Quote':data['Quote Price'],'52WHigh':high52,'52WLow':low52},index=[0])

async def getData(session: aiohttp.ClientSession, ticker, period, interval = '1d'):

    df = pd.DataFrame()
    # This function explicitly uses auto_adjust=True, so we'll keep it that way
    df = await _run_sync_in_executor(yf.download, ticker, period=period, auto_adjust=True, interval=interval)
    return df

#print(getQuoteTable('SPY'))
