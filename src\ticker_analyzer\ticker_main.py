import argparse
import sys
import pandas as pd
import yfinance as yf
import asyncio
import aiohttp

# Import from sibling modules
from ticker_analyzer.config_manager import load_config, save_config
from ticker_analyzer.utils import format_market_cap
from ticker_analyzer.data_fetcher import (
    login_robinhood,
    get_cached_factors,
    fetch_stock_data,
    fetch_basic_info_and_news,
    print_catalyst_news,
    fetch_etf_holdings,
    fetch_fund_holdings
)
from ticker_analyzer.indicators import (
    calculate_atr,
    calculate_rsi,
    calculate_rvol,
    calculate_mas,
    calculate_atr_percentage_and_sma_gain
)
from ticker_analyzer.position_sizer import (
    determine_entry_price,
    determine_stop_loss_price,
    calculate_position_shares,
    compute_stop_tiers
)
from ticker_analyzer.display import (
    display_atr_sma_metrics,
    display_rvol,
    display_atr_change,
    display_rsi,
    display_highs_lows,
    display_mas,
    display_position_sizing_results,
    display_holdings
)


async def main():
    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="Stock Ticker Analysis Tool")
    parser.add_argument("ticker", nargs='?', default=None, help="Optional ticker symbol to analyze.")
    args = parser.parse_args()

    # --- Load Configuration ---
    config = load_config()
    portfolio_capital = config['portfolio_capital']
    run_mode = config['run_mode']
    remember_last_ticker = config['remember_last_ticker']
    default_entry_method = config['default_entry_price_method']
    show_extras = config['default_show_extras']

    # --- Initial Setup ---
    async with aiohttp.ClientSession() as session:
        # Attempt Robinhood Login (do this once outside the loop)
        logged_in_rh = await login_robinhood()
        if not logged_in_rh:
            print("Warning: Robinhood features (historicals, catalyst news) may be limited.")

        # Get factors (cached)
        factors = await get_cached_factors(session)
        print(f'Risk on/off Factor: {factors}') # Display factor early

        last_ticker = config.get('default_ticker', '') # Keep track of the last used ticker

        # --- Main Loop ---
        while True:
            # --- Determine Ticker ---
            current_ticker_arg = args.ticker # Check arg each loop iteration? No, consume it.
            ticker_to_use = None

            if current_ticker_arg: # Use command-line arg if provided (only on first run)
                ticker_to_use = current_ticker_arg.upper()
                args.ticker = None # Consume the arg so it's not reused in loop mode
                print(f"Using command line ticker: {ticker_to_use}")
            elif config['default_ticker']: # Use config default
                ticker_to_use = config['default_ticker'].upper()
                print(f"Using default ticker from config: {ticker_to_use}")
            else: # Prompt if no other source
                try:
                    ticker_input = input("Enter your stock ticker (or press Enter to exit): ").strip().upper()
                    if not ticker_input:
                         print("Exiting.")
                         break # Exit loop if user enters nothing
                    ticker_to_use = ticker_input
                except EOFError:
                     print("\nInput stream closed. Exiting.")
                     break


            if not ticker_to_use: # Should only happen if initial prompt is empty
                print("No ticker provided. Exiting.")
                break

            last_ticker = ticker_to_use # Update last ticker used in this session

            print(f"\n===== Analyzing {ticker_to_use} =====")

            # --- Fetch Data ---
            try:
                # Fetch basic info and Yahoo news first (less prone to failure than RH)
                await fetch_basic_info_and_news(ticker_to_use)

                # Fetch historical data (uses RH and scraping)
                stock_data = await fetch_stock_data(ticker_to_use) # This is cached

                if stock_data.empty:
                    print(f"Could not fetch sufficient historical data for {ticker_to_use}. Skipping analysis.")
                    if run_mode == 'once': break
                    else: continue # Try next ticker in loop mode

            except Exception as e:
                print(f"Error during data fetching phase for {ticker_to_use}: {e}")
                if run_mode == 'once':
                    break
                else:
                    print("Continuing to next ticker...")
                    continue # Skip to next iteration in loop mode

            # --- Calculate Indicators ---
            try:
                # Calculate necessary indicators - functions modify dataframe inplace or return series
                stock_data = calculate_atr(stock_data)
                stock_data = calculate_rvol(stock_data)
                stock_data = calculate_mas(stock_data) # Calculates EMAs/SMAs and boolean flags
                rsi_series = calculate_rsi(stock_data) # Returns a Series

                # Calculate ATR% and SMA gain metrics (requires ATR and SMA50)
                atr_sma_metrics = calculate_atr_percentage_and_sma_gain(stock_data, sma_period=50)

            except Exception as e:
                print(f"Error calculating indicators for {ticker_to_use}: {e}")
                # Decide if we should continue without indicators or skip
                print("Skipping further analysis for this ticker due to indicator calculation error.")
                if run_mode == 'once': break
                else: continue


            # --- Display Basic Indicators (Part of Extras now) ---
            # Moved display logic to the "Extras" section below


            # --- Sizing Calculation ---
            print('\n--- Position Sizing ---')

            # Determine Entry Price
            entry_price = determine_entry_price(stock_data, default_entry_method, ticker_to_use)
            if entry_price is None: # Handle case where user cancels input or error occurs
                print("Could not determine entry price. Skipping sizing.")
                if run_mode == 'once': break
                else: continue

            # Determine Stop Loss
            stop_price = determine_stop_loss_price(entry_price, stock_data, config)
            if stop_price is None: # Handle case where user cancels input or error occurs
                print("Could not determine stop loss price. Skipping sizing.")
                if run_mode == 'once': break
                else: continue

            # Calculate Position Size
            num_shares_full = calculate_position_shares(entry_price, stop_price, portfolio_capital)
            risk_per_share = entry_price - stop_price if entry_price > stop_price else 0

            # Define position configurations for display
            position_configs = [
                ('Quarter', num_shares_full / 4),
                ('Half', num_shares_full / 2),
                ('Full', num_shares_full)
            ]

            # Calculate stop tiers for each position size
            stops_data = {}
            for name, size in position_configs:
                 # Ensure size is non-negative before rounding and passing
                 actual_size = max(0, round(size))
                 stops_data[name] = compute_stop_tiers(stop_price, risk_per_share, actual_size)


            # --- Display Sizing Results ---
            display_position_sizing_results(entry_price, stop_price, risk_per_share, position_configs, stops_data)


            # --- Extras Section ---
            if show_extras:
               print("\n--- Technical Indicators & Extras ---")
               try:
                   display_rvol(stock_data)
                   display_atr_change(stock_data)
                   display_rsi(rsi_series)
                   display_atr_sma_metrics(atr_sma_metrics) # Display calculated metrics
                   display_highs_lows(stock_data) # Includes calculation now
                   display_mas(stock_data) # Displays MAs calculated earlier
               except Exception as e:
                   print(f"Error displaying technical indicators: {e}")

               # News and Holdings
               try:
                   if logged_in_rh: # Only try RH news if logged in
                        # Fetch Robinhood news (function handles printing)
                        rh_news = await print_catalyst_news(ticker_to_use) # Directly prints
                   else:
                        print("\nSkipping Robinhood News (not logged in).")

                   # Fetch and display holdings (functions handle printing)
                   etf_holdings_df = await fetch_etf_holdings(session, ticker_to_use) # Directly prints
                   fund_holdings_df = await fetch_fund_holdings(session, ticker_to_use) # Directly prints

               except Exception as e:
                   print(f"Error fetching/displaying news or holdings: {e}")


            # --- Loop Control ---
            if run_mode == 'once':
                print("\nRun mode is 'once'. Exiting.")
                break # Exit loop if run_mode is 'once'
            else:
                print("\n------------------------------ Next Ticker ------------------------------")
                # Loop automatically. Prompt is handled at the start of the loop.


        # --- Save Config (Optional) ---
        if remember_last_ticker and last_ticker:
            config['default_ticker'] = last_ticker # Save the very last ticker used
            save_config(config)
            print(f"\nSaved {last_ticker} as default ticker for next run.")

        # Consider adding r.logout() here if login was successful?
        # Needs careful handling if login failed or wasn't attempted.


if __name__ == "__main__":
    asyncio.run(main())
