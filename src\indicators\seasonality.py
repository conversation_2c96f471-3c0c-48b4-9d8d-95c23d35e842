import datetime



def getSeasonality(df):
    #df =

    df = df.resample('D').ffill()

    #print(df.tail(14))

    df['Returns'] = round(df['Close'].resample('M').ffill().pct_change()*100,2)


    df.dropna(subset = ['Returns'],inplace=True)

    #print(df)

    val = df.groupby(df.index.month)['Returns'].mean().sort_values()#apply(list)

    return val[datetime.date.today().month]

#print(getSeasonality(getStockDataV3('JNJ')))


# print(netflix_monthly_returns)