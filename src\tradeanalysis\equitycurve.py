import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# Load data from Excel file
df = pd.read_excel(r'C:\Users\<USER>\Desktop\trade_analysis\trades.xlsx')

# Convert dates to datetime format
df['SellDate'] = pd.to_datetime(df['SellDate'])

# Optional: Use SellDate if it makes more sense to use the completion date
df = df[df['SellPrice'] != 0]

df = df[df['BuyPrice'] != 0]

# Sort by SellDate
df.sort_values(by='SellDate', inplace=True)

# Calculate the cumulative P/L
df['Cumulative P/L'] = df['P/L'].cumsum()

# Create the plot
plt.figure(figsize=(12, 6))

# Use matplotlib's date plotting capabilities
plt.plot(df['SellDate'].tolist(), df['Cumulative P/L'].tolist(), marker='o')

plt.title('Equity Curve')
plt.xlabel('Date')
plt.ylabel('Cumulative P/L')
plt.grid(True)

# Format x-axis to handle dates nicely
plt.gcf().autofmt_xdate()
plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))

plt.tight_layout()

# Display the plot
plt.show()