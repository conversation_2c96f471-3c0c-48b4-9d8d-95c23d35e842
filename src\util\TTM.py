import yfinance as yf
import pandas as pd
import numpy as np
import talib as ta
from common.http_session import get_session

session = get_session('src/certs/my_ca_bundle.pem')

def calculate_squeeze_momentum(data, length=20, mult=1.5, lengthKC=20, multKC=1.5):
    """
    Calculate the Squeeze Momentum indicator (LazyBear version)
    Returns a series of boolean values indicating if squeeze is on
    """


    # Convert DataFrame columns to NumPy arrays for TA-Lib
    close_array = data['Close'].to_numpy()
    high_array = data['High'].to_numpy()
    low_array = data['Low'].to_numpy()

    # Calculate Bollinger Bands
    bb_middle = ta.SMA(close_array, length)
    bb_std = ta.STDDEV(close_array, length)
    bb_upper = bb_middle + (mult * bb_std)
    bb_lower = bb_middle - (mult * bb_std)

    # Calculate Keltner Channels
    kc_middle = ta.SMA(close_array, lengthKC)
    tr = ta.TRANGE(high_array, low_array, close_array)
    tr_ma = ta.SMA(tr, lengthKC)
    kc_upper = kc_middle + (multKC * tr_ma)
    kc_lower = kc_middle - (multKC * tr_ma)

    # Calculate squeeze conditions
    squeeze_on = (bb_lower > kc_lower) & (bb_upper < kc_upper)

    # Calculate linear regression value (not needed for squeeze on/off series)
    highest_high = data['High'].rolling(window=lengthKC).max()
    lowest_low = data['Low'].rolling(window=lengthKC).min()
    avg_hl = (highest_high + lowest_low) / 2
    avg_hlc = (avg_hl + kc_middle) / 2

    # Return the squeeze on/off series
    return pd.Series(squeeze_on, index=data.index)

# Example usage
if __name__ == "__main__":
    # Example for SPY
    symbol = "DOCU"
    # Get data from Yahoo Finance
    data = yf.download(symbol, period='1y',session=session)
    squeeze = calculate_squeeze_momentum(data)

    # Print the last 5 values
    print(f"Last 5 squeeze values for {symbol}:")
    print(squeeze.tail(15))

    # Print summary of squeeze conditions
    print(f"\nSqueeze is currently {'ON' if squeeze.iloc[-1] else 'OFF'}")
    print(f"Number of squeeze ON signals in the period: {squeeze.sum()}")
    print(f"Percentage of time in squeeze: {(squeeze.sum() / len(squeeze) * 100):.2f}%")