import urllib.parse
import json

# # Original encoded query string
# encoded_query = "AjaxId=1&Criteria=%5B%7B%22ArgsOperator%22%3Anull%2C%22Arguments%22%3A%5B%5D%2C%22Clauses%22%3A%5B%7B%22Operator%22%3A%22LessThanOrEqualTo%22%2C%22Values%22%3A%5B245.46%5D%7D%2C%7B%22Operator%22%3A%22GreaterThanOrEqualTo%22%2C%22Values%22%3A%5B0.25%5D%7D%5D%2C%22ClauseGroups%22%3A%5B%5D%2C%22Field%22%3A%22FI.Vol90dAvg%22%2C%22Identifiers%22%3A%5B%5D%7D%2C%7B%22ArgsOperator%22%3Anull%2C%22Arguments%22%3A%5B%5D%2C%22Clauses%22%3A%5B%5D%2C%22ClauseGroups%22%3A%5B%5D%2C%22Field%22%3A%22FI.MarketCap%22%2C%22Identifiers%22%3A%5B%5D%7D%2C%7B%22ArgsOperator%22%3Anull%2C%22Arguments%22%3A%5B%5D%2C%22Clauses%22%3A%5B%7B%22Operator%22%3A%22LessThanOrEqualTo%22%2C%22Values%22%3A%5B724106.47%5D%7D%2C%7B%22Operator%22%3A%22GreaterThanOrEqualTo%22%2C%22Values%22%3A%5B8%5D%7D%5D%2C%22ClauseGroups%22%3A%5B%5D%2C%22Field%22%3A%22FI.IntradayPrice%22%2C%22Identifiers%22%3A%5B%5D%7D%2C%7B%22ArgsOperator%22%3Anull%2C%22Arguments%22%3A%5B%5D%2C%22Clauses%22%3A%5B%7B%22Operator%22%3A%22GreaterThanOrEqualTo%22%2C%22Values%22%3A%5B0%5D%7D%5D%2C%22ClauseGroups%22%3A%5B%5D%2C%22Field%22%3A%22FI.IntradayPricePerformance%22%2C%22Identifiers%22%3A%5B%22%7B%5C%22filters%5C%22%3A%5B0%2C1%2C2%2C3%2C4%5D%7D%22%5D%7D%2C%7B%22ArgsOperator%22%3Anull%2C%22Arguments%22%3A%5B%5D%2C%22Clauses%22%3A%5B%7B%22Operator%22%3A%22LessThanOrEqualTo%22%2C%22Values%22%3A%5B959.3%5D%7D%2C%7B%22Operator%22%3A%22GreaterThanOrEqualTo%22%2C%22Values%22%3A%5B2%5D%7D%5D%2C%22ClauseGroups%22%3A%5B%5D%2C%22Field%22%3A%22FI.IntradayVolVol10dAvg%22%2C%22Identifiers%22%3A%5B%5D%7D%5D&ResultView=SearchCriteria&FirstRow=0&RowCount=100&SortDir=&SortField=&SortResults=true&InitialLoad=true&ScreenerId=128&SelectedOnlyUserAdded=0"

# # Parse the query string into a dictionary
# query_dict = urllib.parse.parse_qs(encoded_query)

criteria = [
    {'ArgsOperator': None, 'Arguments': [], 'Clauses': [{'Operator': 'LessThanOrEqualTo', 'Values': [245.46]}, {'Operator': 'GreaterThanOrEqualTo', 'Values': [0.25]}], 'ClauseGroups': [], 'Field': 'FI.Vol90dAvg', 'Identifiers': []},
    {'ArgsOperator': None, 'Arguments': [], 'Clauses': [], 'ClauseGroups': [], 'Field': 'FI.MarketCap', 'Identifiers': []},
    {'ArgsOperator': None, 'Arguments': [], 'Clauses': [{'Operator': 'LessThanOrEqualTo', 'Values': [724106.47]}, {'Operator': 'GreaterThanOrEqualTo', 'Values': [8]}], 'ClauseGroups': [], 'Field': 'FI.IntradayPrice', 'Identifiers': []},
    {'ArgsOperator': None, 'Arguments': [], 'Clauses': [{'Operator': 'GreaterThanOrEqualTo', 'Values': [0]}], 'ClauseGroups': [], 'Field': 'FI.IntradayPricePerformance', 'Identifiers': ['{"filters":[0,1,2,3,4]}']},
    {'ArgsOperator': None, 'Arguments': [], 'Clauses': [{'Operator': 'LessThanOrEqualTo', 'Values': [959.3]}, {'Operator': 'GreaterThanOrEqualTo', 'Values': [2]}], 'ClauseGroups': [], 'Field': 'FI.IntradayVolVol10dAvg', 'Identifiers': []}
]


def getQuery(rvol):
# Find and update the clause
 for clause in criteria:
     if clause['Field'] == 'FI.IntradayVolVol10dAvg':
         for sub_clause in clause['Clauses']:
             if sub_clause['Operator'] == 'GreaterThanOrEqualTo':
                 sub_clause['Values'][0] = rvol
                 break

 # Convert the JSON object back to a string
 criteria_str = json.dumps(criteria)

 # Reconstruct the query string with the updated 'Criteria' value
 updated_query = urllib.parse.urlencode({
     'AjaxId': 1,
     'Criteria': criteria_str,
     'ResultView': 'SearchCriteria',
     'FirstRow': 0,
     'RowCount': 500, # this is max
     'SortDir': '',
     'SortField': 'FI.Vol90dAvg',
     'SortResults': True,
     'InitialLoad': True,
     'ScreenerId': 128,
     'SelectedOnlyUserAdded': 0
 })

 return (updated_query)
