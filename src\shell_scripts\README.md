# Shell Scripts for Raspbian Linux

This directory contains shell script equivalents of the Windows batch files for running the PythonicFin tools on Raspbian Linux.

## Installation

The easiest way to set up the environment is to use the provided setup script:

```bash
./src/shell_scripts/setup_raspbian.sh
```

This script will:
1. Create a Python virtual environment in the project root directory
2. Install all required dependencies (except TA-Lib, which requires the separate `install_talib.sh` script)
3. Create necessary data directories
4. Make all shell scripts executable

### Manual Setup

If you prefer to set up manually, make sure you have:

1. Set up a Python virtual environment in the project root directory:
   ```bash
   python3 -m venv venv
   ```

2. Installed all required dependencies:
   ```bash
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. Created a `.env` file in the project root with necessary environment variables
   (You can use the provided `.env.sample` file as a template)

## Available Scripts

### All-in-One Wrapper Script

The `run_tool.sh` script provides a convenient way to run any of the tools:

```bash
./src/shell_scripts/run_tool.sh [tool] [arguments]
```

Available tools:
- `ticker [symbol]` - Run ticker analysis for the specified symbol
- `screener` - Run the market screener
- `enhanced` - Run enhanced chart analysis

Examples:
```bash
./src/shell_scripts/run_tool.sh ticker AAPL
./src/shell_scripts/run_tool.sh screener
./src/shell_scripts/run_tool.sh enhanced
```

### Individual Scripts

You can also use the individual scripts directly:

#### Ticker Analysis

Run the ticker analysis tool:
```bash
./src/shell_scripts/ticker.sh AAPL
```

This will analyze the AAPL ticker with default settings.

#### Market Screening

Run the market screener:
```bash
./src/shell_scripts/screener.sh
```

#### Enhanced Analysis

Run the enhanced chart analysis:
```bash
./src/shell_scripts/enhanced_analysis.sh
```

## Making Scripts Executable

Before using the scripts, make them executable:
```bash
chmod +x src/shell_scripts/*.sh
```

## Installing TA-Lib

TA-Lib is a technical analysis library used by some parts of the codebase. It requires special installation steps on Raspbian. Use the provided script:

```bash
./src/shell_scripts/install_talib.sh
```

This script will:
1. Install necessary build dependencies
2. Download and compile TA-Lib from source
3. Install the Python wrapper

Note: This process may take some time on a Raspberry Pi due to the compilation step.

## Differences from Windows Batch Files

These shell scripts are designed to work on Raspbian Linux and have some differences from their Windows counterparts:

1. They use the Linux virtual environment structure (`venv/bin/activate` instead of `pythonicfin_env_py311\Scripts\activate.bat`)
2. Environment variables are loaded differently (using `export` instead of `set`)
3. Path separators use forward slashes (`/`) instead of backslashes (`\`)
4. The scripts use `cd "$(dirname "$0")/../.."` to navigate to the project root directory

## Raspbian-Specific Considerations

When running these scripts on Raspbian:

1. **Python Version**:
   - The Windows version of this project uses Python 3.11
   - Raspbian typically comes with an older version of Python 3 pre-installed (often 3.7 or 3.9)
   - The setup script will check your Python version and warn if it's too old
   - Python 3.7+ is recommended, but some features may require Python 3.9+
   - If you need Python 3.11, you can use the provided `install_python3.11.sh` script
     ```bash
     ./src/shell_scripts/install_python3.11.sh
     ```
     Note: Building Python from source on a Raspberry Pi can take several hours

2. **Memory Limitations**: Raspberry Pi has limited RAM. For memory-intensive operations:
   - Consider increasing swap space
   - Limit the number of stocks processed at once
   - Close unnecessary applications when running analysis

3. **Performance**: Some operations may be slower on Raspberry Pi:
   - Chart downloads might take longer
   - Data processing for large datasets will be slower
   - Consider using smaller datasets for testing

4. **Display**: If you're using a headless Raspberry Pi (no monitor):
   - Functions that open files (like `os.startfile()`) won't work
   - Consider modifying the code to save files without opening them
   - Use remote file access to view generated documents

5. **Dependencies**:
   - Some Python packages may require compilation, which can be slow on Raspberry Pi
   - Use pre-compiled wheels when available
   - TA-Lib requires special installation steps on Raspbian (use the provided `install_talib.sh` script)

## Troubleshooting

If you encounter issues:

1. Make sure the scripts are executable (`chmod +x src/shell_scripts/*.sh`)
2. Verify that your virtual environment is set up correctly
3. Check that all required Python packages are installed
4. Ensure your `.env` file contains all necessary environment variables
