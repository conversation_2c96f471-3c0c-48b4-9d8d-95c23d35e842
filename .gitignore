# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover

# Logs and databases
*.log
*.sqlite
*.db

# Environment variables and secrets
.env
!.env.sample
*.env
src/.env
.venv
env/
venv/
ENV/

# Credentials and tokens
*.pickle
token.*
*credentials*.json
*token*.json
secret_scan_results.csv
sensitive-patterns.txt
sensitive-text.txt
specific-sensitive-text.txt

# Security scanning scripts
*scan*.ps1
analyze_git_history.ps1
git_history_analysis/
git-scan-temp/

# Cache directories
cache/
tmp/
.tmp/

# Data files
*.csv
*.xlsx
*.xls
*.html
*.docx
*.jpg
*.png
*.json
!src/data/*.csv

# Project specific
src/test.py
src/config.py
src/ds_learning
src/prac
memory-bank/

# Large binary files
*.exe
*.zip
*.xpi
trufflehog_scan.txt
phantomjs*
chromedriver*
geckodriver*
adguard_adblocker*
alpaca-postman*

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
bfg.jar

pythonicfin_env/
pythonicfin_venv/
pythonicfin_env_py311/

# Python wheel files
*.whl

# Backup files
*_py*.bat
src/.env.sample
