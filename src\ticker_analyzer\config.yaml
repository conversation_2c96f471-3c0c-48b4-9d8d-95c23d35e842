# Configuration for PythonicFin Ticker Analysis Tool

# --- General Settings ---
portfolio_capital: 100000       # Total portfolio value for sizing calculations
run_mode: loop                  # Script execution mode. Options: loop, once
                                # 'loop': Runs continuously, prompting for new tickers.
                                # 'once': Runs for one ticker then exits.

# --- Ticker Settings ---
default_ticker: ""             # Default ticker to analyze if none provided via command line.
                                # Leave blank ("") to always prompt if no command-line ticker is given.
remember_last_ticker: false      # If true, update 'default_ticker' above with the last analyzed ticker upon exit/loop.

# --- Sizing Settings ---
default_entry_price_method: last_close # How to determine entry price if not prompted.
                                       # Options: last_close, prompt
default_stop_loss_type: atr2           # Default stop loss strategy.
                                       # Options: atr2, atr3, price, prompt
default_stop_loss_value: 8.0           # Default stop loss price/percentage value.
                                       # Only used if default_stop_loss_type is 'price'.
                                       # Example: For a price target, use the price. For a percentage, use e.g. 0.98 for 2% below entry.
                                       # Note: The script currently expects a price, not a percentage, if 'price' type is chosen interactively.
                                       # We might need to adjust the script or clarify this setting's usage.

# --- Output Settings ---
default_show_extras: true       # Whether to automatically calculate and show extra info (RVOL, ATR, RSI, Highs/Lows, MAs, News, Holdings).
                                # If false, these sections are skipped.

# --- Credentials ---
# Robinhood credentials should be handled securely, e.g., via environment variables or a separate secure mechanism.
# This file is NOT the place for passwords.
# The script currently imports rh_un, rh_pw from 'config' module. Ensure that module exists and is secure.
