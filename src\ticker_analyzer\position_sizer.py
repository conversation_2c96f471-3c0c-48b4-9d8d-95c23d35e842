import pandas as pd
import numpy as np # Import numpy for potential use, though not directly used here yet
from typing import Optional, Union, List, Tuple # Ensure List and Tuple are imported

# Import from sibling modules
from .indicators import calculate_atr_stop_value

def determine_entry_price(stock_data: pd.DataFrame, method: str, ticker: str) -> Optional[float]:
    """Determines the entry price based on the specified method or prompts the user."""
    entry_price = None
    if method == 'last_close' and not stock_data.empty and 'close_price' in stock_data.columns:
        entry_price = stock_data['close_price'].iloc[-1]
        print(f"Using last close price as entry: {entry_price:.2f}")
    else: # Default to prompt if method is not 'last_close' or data is missing
        while entry_price is None:
            try:
                entry_price_str = input(f"Enter entry price for {ticker}: ").strip()
                if not entry_price_str:
                    print("Entry price cannot be empty.")
                    continue
                entry_price = float(entry_price_str)
                if entry_price <= 0:
                    print("Entry price must be positive.")
                    entry_price = None # Reset to loop again
            except ValueError:
                print("Invalid entry price entered. Please enter a number.")
            except EOFError: # Handle case where input stream is closed unexpectedly
                 print("\nInput stream closed. Cannot get entry price.")
                 return None
    return entry_price


def determine_stop_loss_price(entry_price: float, stock_data: pd.DataFrame, config: dict) -> Optional[float]:
    """Determines the stop loss price based on config settings or prompts."""
    stop_loss_type = config.get('default_stop_loss_type', 'atr2') # Default to atr2 if not specified
    stop_price = None
    user_choice_made = False # Flag to track if user explicitly chose via prompt

    if stop_loss_type == 'prompt':
        print('Choose stop loss type:')
        print('1. Price-based stop loss')
        print('2. 2x ATR-based stop loss')
        print('3. 3x ATR-based stop loss')
        try:
            user_choice = input('Enter your choice (1/2/3) or press Enter for default (Price): ').strip()
            user_choice_made = True
        except EOFError:
            print("\nInput stream closed. Cannot get stop loss type.")
            return None


        if user_choice == '2':
            stop_loss_type = 'atr2'
        elif user_choice == '3':
            stop_loss_type = 'atr3'
        else: # Default or '1'
            stop_loss_type = 'price'

    # Calculate stop price based on type
    if stop_loss_type == 'price':
        user_stop_loss = config.get('default_stop_loss_value', 0.0) # Use default first
        # Prompt only if default is 0.0 or type was explicitly 'prompt'
        if user_stop_loss <= 0.0 or user_choice_made: # Also prompt if user chose '1'
             while True:
                 try:
                     stop_loss_str = input('Enter the price-based stop loss: ').strip()
                     if not stop_loss_str:
                         print("Stop loss price cannot be empty.")
                         continue
                     user_stop_loss = float(stop_loss_str)
                     if user_stop_loss <= 0:
                         print("Stop loss price must be positive.")
                         continue
                     if user_stop_loss >= entry_price:
                         print("Stop loss must be below the entry price.")
                         continue
                     break # Valid input
                 except ValueError:
                     print("Invalid stop loss price. Please enter a number.")
                 except EOFError:
                     print("\nInput stream closed. Cannot get stop loss price.")
                     return None
        stop_price = calculate_final_stop_price(entry_price, stock_data, user_stop_loss=user_stop_loss)
        print(f"Using Price-based stop: {stop_price:.2f}")
    elif stop_loss_type == 'atr2':
        stop_price = calculate_final_stop_price(entry_price, stock_data, multiplier=2)
        print(f"Using 2x ATR-based stop (max 8% cap applied if needed): {stop_price:.2f}")
    elif stop_loss_type == 'atr3':
        stop_price = calculate_final_stop_price(entry_price, stock_data, multiplier=3)
        print(f"Using 3x ATR-based stop (max 8% cap applied if needed): {stop_price:.2f}")
    else:
        print(f"Invalid stop_loss_type in config: {stop_loss_type}. Skipping sizing.")
        return None

    # Final validation
    if stop_price is None or stop_price >= entry_price:
         print("Invalid stop price calculated (must be below entry price). Skipping sizing.")
         return None

    return stop_price


def calculate_final_stop_price(entry_price: float, stock_data: pd.DataFrame, user_stop_loss: Optional[float] = None, multiplier: Optional[int] = None) -> float:
    """
    Calculates the stop price based on ATR multiplier or a user-defined price,
    applying an 8% max loss cap.
    """
    calculated_stop = None
    if multiplier:
        atr_value = calculate_atr_stop_value(stock_data) # Get latest ATR value
        if atr_value > 0:
            calculated_stop = entry_price - (atr_value * multiplier)
        else:
            print("Warning: ATR value is zero or could not be calculated. Cannot use ATR stop.")
            # Fallback or error? For now, fallback to max loss.
            calculated_stop = entry_price * 0.92
    elif user_stop_loss is not None:
        calculated_stop = user_stop_loss
    else:
        # Should not happen if called correctly, but default to max loss
        print("Warning: No stop loss method specified. Defaulting to 8% max loss.")
        calculated_stop = entry_price * 0.92

    # Ensure calculated_stop is not None before comparison
    if calculated_stop is None:
         print("Error: Could not determine initial stop price. Using 8% max loss.")
         calculated_stop = entry_price * 0.92


    max_stop_loss_price = entry_price * 0.92  # 8% from the entry price

    final_stop_price = calculated_stop

    # Apply the 8% max loss cap
    if final_stop_price < max_stop_loss_price:
        final_stop_price = max_stop_loss_price
        print("Note: Stop loss capped at an 8% maximum loss from the entry price.")
    elif final_stop_price >= entry_price:
         print("Warning: Calculated stop loss is at or above entry price. Adjusting to 8% max loss.")
         final_stop_price = max_stop_loss_price


    return final_stop_price


def calculate_position_shares(entry_price: float, stop_price: float, portfolio_capital: float, max_risk_pct: float = 0.01, max_capital_pct: float = 0.20) -> int:
    """
    Calculates the number of shares based on risk and capital constraints.
    Returns integer number of shares.
    """
    if entry_price <= stop_price:
        print("Error: Entry price must be greater than stop price for position sizing.")
        return 0

    max_loss_amt = portfolio_capital * max_risk_pct
    max_capital_per_trade = max_capital_pct * portfolio_capital
    risk_per_share = entry_price - stop_price

    if risk_per_share <= 0:
         print("Error: Risk per share is zero or negative. Cannot calculate position size.")
         return 0

    num_shares = int(max_loss_amt / risk_per_share)

    # Ensure capital doesn't exceed maximum allowed
    share_capital = num_shares * entry_price
    if share_capital > max_capital_per_trade:
        print(f"Note: Position size adjusted to meet {max_capital_pct*100:.0f}% max capital constraint.")
        num_shares = int(max_capital_per_trade / entry_price)

    # Ensure shares are not negative
    num_shares = max(0, num_shares)

    return num_shares


def compute_stop_tiers(base_stop_price: float, risk_per_share: float, num_shares: int) -> List[Tuple[int, float]]: # Already correct, just ensuring import
    """
    Computes three stop tiers based on 33% increments of the risk per share.
    Returns a list of (shares_to_sell, stop_price) tuples, sorted descending by price.
    """
    if num_shares <= 0 or risk_per_share <= 0:
        return [] # No tiers if no shares or no risk

    stop_1 = base_stop_price + risk_per_share * 0.66 # Highest stop (sell first third)
    stop_2 = base_stop_price + risk_per_share * 0.33 # Middle stop (sell second third)
    stop_3 = base_stop_price                     # Final stop (sell last third)

    # Calculate shares per tier, ensuring whole numbers and accounting for rounding
    size_tier = max(1, round(num_shares / 3)) # Ensure at least 1 share per tier if possible

    # Distribute shares, adjusting the last tier
    shares_tier1 = size_tier
    shares_tier2 = size_tier
    # Ensure total shares match num_shares, handle small share counts
    shares_tier3 = max(0, num_shares - shares_tier1 - shares_tier2)

    # If total shares are less than 3, adjust distribution
    if num_shares == 1:
        shares_tier1, shares_tier2, shares_tier3 = 0, 0, 1
    elif num_shares == 2:
        shares_tier1, shares_tier2, shares_tier3 = 0, 1, 1 # Or 1, 1, 0 - let's put more on lower stops

    stops_list = []
    # Add tiers only if they have shares assigned
    if shares_tier1 > 0: stops_list.append((shares_tier1, round(stop_1, 2)))
    if shares_tier2 > 0: stops_list.append((shares_tier2, round(stop_2, 2)))
    if shares_tier3 > 0: stops_list.append((shares_tier3, round(stop_3, 2)))

    # Sort by stop price descending (highest stop first)
    return sorted(stops_list, key=lambda x: x[1], reverse=True)
