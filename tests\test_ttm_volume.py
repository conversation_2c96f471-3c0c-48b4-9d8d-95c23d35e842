"""
Test script to verify that the TTM and volume calculations are working correctly.
"""

import sys
import os
import pandas as pd
import numpy as np

# Add src directory to Python path
sys.path.append('src')

from stockdata.data_source import getStockDataV3
from util.TTM import calculate_squeeze_momentum

def test_ttm_volume():
    """Test the TTM and volume calculations."""
    print("Testing TTM and volume calculations with ticker 'AAPL'...")
    
    # Get stock data for Apple
    df = getStockDataV3('AAPL')
    
    if df is not None and not df.empty:
        print("Success! Retrieved data for AAPL.")
        
        # Check if the TTM column exists and has non-placeholder values
        if 'TTM' in df.columns:
            ttm_values = df['TTM'].value_counts()
            print("\nTTM values distribution:")
            print(ttm_values)
            
            if len(ttm_values) > 1:  # If there are both True and False values
                print("✅ TTM calculation is working correctly!")
            else:
                print("❌ TTM calculation might be using placeholder values only.")
        else:
            print("❌ TTM column not found in the DataFrame.")
        
        # Check if the $VolumeM column exists and has meaningful values
        if '$VolumeM' in df.columns:
            volume_stats = df['$VolumeM'].describe()
            print("\n$VolumeM statistics:")
            print(volume_stats)
            
            if volume_stats['std'] > 0:  # If there's variation in the values
                print("✅ Volume calculation is working correctly!")
            else:
                print("❌ Volume calculation might be using placeholder values only.")
        else:
            print("❌ $VolumeM column not found in the DataFrame.")
        
        # Print the first few rows to see the actual values
        print("\nFirst 5 rows with TTM and $VolumeM columns:")
        print(df[['TTM', '$VolumeM']].head())
        
        return True
    else:
        print("Failed to retrieve data for AAPL")
        return False

if __name__ == "__main__":
    test_ttm_volume()
