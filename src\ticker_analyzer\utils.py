import hashlib
import os
import datetime as dt
import pickle
from datetime import datetime
from typing import Any, Callable, TypeVar, Union

# Define type variables for generic decorator handling
T = TypeVar('T')  # Return type of the decorated function

CACHE_DIR = "cache/"

def cache_for_one_hour(func: Callable[..., T]) -> Callable[..., T]:
    """Decorator to cache function results for one hour."""
    if not os.path.exists(CACHE_DIR):
        os.makedirs(CACHE_DIR)

    async def wrapper(*args: Any, **kwargs: Any) -> T:
        # Generate a unique cache key based on function name, arguments, and keyword arguments
        # Use a stable representation of args and kwargs
        arg_str = str(args) + str(sorted(kwargs.items()))
        cache_key_input = (func.__name__ + arg_str).encode()
        cache_key = hashlib.md5(cache_key_input).hexdigest()
        cache_file = os.path.join(CACHE_DIR, cache_key)

        # Check if cached result exists and if it's still valid
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cached_result, timestamp = pickle.load(f)
                    if datetime.now() - timestamp < dt.timedelta(hours=1):
                        # print(f"Cache hit for {func.__name__} with key: {cache_key}")
                        return cached_result
            except (EOFError, pickle.UnpicklingError) as e:
                print(f"Cache file {cache_file} corrupted, ignoring. Error: {e}")
                os.remove(cache_file) # Remove corrupted file

        # print(f"Cache miss for {func.__name__} with key: {cache_key}")
        result = await func(*args, **kwargs)

        # Save result to cache
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump((result, datetime.now()), f)
        except Exception as e:
            print(f"Error writing to cache file {cache_file}: {e}")

        return result

    return wrapper

def str_to_num(s: str) -> float:
    """Converts strings like '1.23M' or '500K' to float numbers."""
    s = str(s).strip() # Ensure it's a string and remove whitespace
    d = {"K": 1000, "M": 1000000, "B": **********}
    if not s:
        return 0.0 # Handle empty strings
    if s[-1].upper() in d:
        try:
            num_part = s[:-1]
            # Handle potential commas in the number part
            num_part = num_part.replace(',', '')
            return float(num_part) * d[s[-1].upper()]
        except ValueError:
            print(f"Warning: Could not convert numeric part of '{s}' to float.")
            return 0.0 # Return 0 or raise error if conversion fails
    else:
        try:
            # Handle potential commas
            s = s.replace(',', '')
            return float(s)
        except ValueError:
            print(f"Warning: Could not convert '{s}' to float.")
            return 0.0 # Return 0 or raise error

def format_market_cap(value: Union[int, float, Any]) -> str:
    """Formats a large number into a human-readable market cap string (B, M, K)."""
    if not isinstance(value, (int, float)):
        return 'N/A' # Handle non-numeric input
    if value >= 1_000_000_000:
        return f"{value / 1_000_000_000:.2f}B"  # Format as billions
    elif value >= 1_000_000:
        return f"{value / 1_000_000:.2f}M"  # Format as millions
    elif value >= 1_000:
        return f"{value / 1_000:.2f}K"  # Format as thousands
    else:
        return str(value)  # Less than a thousand
