#!/bin/bash

# Script to install TA-Lib on Raspbian Linux
# TA-Lib is a technical analysis library that requires special installation steps

# Change to the project root directory
cd "$(dirname "$0")/../.."
PROJECT_ROOT=$(pwd)

echo "Installing TA-Lib on Raspbian..."

# Check if Python virtual environment exists
if [ ! -d "venv" ]; then
    echo "Python virtual environment not found. Please run setup_raspbian.sh first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
sudo apt-get update
sudo apt-get install -y build-essential wget

# Create a temporary directory for TA-Lib installation
mkdir -p tmp
cd tmp

# Download and extract TA-Lib
echo "Downloading TA-Lib source..."
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xzf ta-lib-0.4.0-src.tar.gz
cd ta-lib/

# Configure and build TA-Lib
echo "Building TA-Lib (this may take a while)..."
./configure --prefix=/usr
make
sudo make install

# Install Python wrapper
echo "Installing Python wrapper for TA-Lib..."
cd $PROJECT_ROOT
pip install TA-Lib

# Clean up
echo "Cleaning up..."
rm -rf tmp

echo ""
echo "TA-Lib installation complete!"
echo ""
echo "If you encounter any issues, you may need to manually install TA-Lib."
echo "See https://github.com/mrjbq7/ta-lib for more information."
