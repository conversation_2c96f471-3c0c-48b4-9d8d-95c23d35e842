import aiohttp
from bs4 import BeautifulSoup
from fidelity.common.headers import get_screnner_headers
from dotenv import load_dotenv
from util.util import read_and_filter_csv
from fidelity.common.screener_query import getQuery as fq

load_dotenv()

URL = "https://research2.fidelity.com/pi/stock-screener/LoadResults"

async def intra_screen(session: aiohttp.ClientSession, rvol: float):
    """
    Performs an asynchronous intraday stock screen using Fidelity's screener.
    """
    payload = fq(rvol)
    headers = get_screnner_headers()

    try:
        async with session.post(URL, headers=headers, data=payload) as response:
            response.raise_for_status()
            resp_json = await response.json()

            # The rest of the function is CPU-bound, so it doesn't need to be async
            soup = BeautifulSoup(resp_json.get("html", ""), "html.parser")

            symbols = []
            for row in soup.find_all("tr"):
                symbol_tag = row.find("a", class_="symbol strong")
                if symbol_tag:
                    symbols.append(symbol_tag.text.split("/")[0])

            if not symbols:
                return []

            rs_filtered_df = read_and_filter_csv(78)
            ticker_list = rs_filtered_df['Ticker'].tolist()

            present_symbols = [symbol for symbol in symbols if symbol in ticker_list]
            return present_symbols

    except aiohttp.ClientError as e:
        print(f"Error during intra_screen request: {e}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred in intra_screen: {e}")
        return []
