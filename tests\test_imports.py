"""
Test script to verify that all required packages are installed correctly.
"""

print("Testing imports...")

# Core data processing and analysis
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sklearn
import yfinance as yf
import pandas_ta as ta

# Web and API interaction
import requests
from bs4 import BeautifulSoup
import os
from dotenv import load_dotenv
import diskcache

# Document handling
import docx
import openpyxl
import xlrd

# Financial data and trading
import backtrader
import exchange_calendars
import robin_stocks

# Utilities
import pytz
import tqdm
import dateutil

print("All imports successful!")

# Test creating a simple DataFrame
df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
print("\nSample DataFrame:")
print(df)

# Test matplotlib
plt.figure(figsize=(6, 4))
plt.plot([1, 2, 3, 4], [1, 4, 9, 16], 'ro-')
plt.title('Test Plot')
plt.savefig('test_plot.png')
plt.close()
print("\nCreated test plot: test_plot.png")

print("\nAll tests completed successfully!")
