#from util.scrapper import downloadChartsFidelity,downloadChartsFidelityV2
import pandas as pd
from util.util import read_and_filter_csv # Corrected absolute import from root
import glob
from fidelity.chart_analysis.download import downloadChartsFidelityV2 # Corrected absolute import from root

def convergence(screener):


    screener = screener.dropna(subset=['% Price off 20 Day SMA'])
    screener = screener.sort_values(by='% Price off 20 Day SMA')
    screener['Convergence'] = screener[['% Price off 50 Day SMA', '% Price off 20 Day SMA','% Price off 10 Day SMA']].diff().abs().sum(axis=1)
    df_sorted = screener.sort_values(by='Convergence', ascending=True)
    df_sorted.to_excel("sorted_screener_convg.xlsx")
    return screener

def printFilteredData(tickers):

     stock_symbols = [entry for entry in tickers if isinstance(entry, str) and len(entry) <= 5]

     print(stock_symbols)

     print(len(stock_symbols))

def  groupAndFilter(df,filter):

    # Remove rows where 'Security Type' is 'Common Stock (REIT)'
    df = df[df['Security Type'] != 'Common Stock (REIT)']

    # Get the count of occurrences within each 'Sector'
    sector_counts = df['Sector'].value_counts().reset_index(name='Sector_Count')
    sector_counts.columns = ['Sector', 'Sector_Count']

    # Merge the count back into the original DataFrame
    df = pd.merge(df, sector_counts, on='Sector')

    # Calculate the size of each group
    df['Sector_Count'] = df.groupby('Sector')['Percentile'].transform('size')
    df['Industry_Count'] = df.groupby(['Sector', 'Industry'])['Percentile'].transform('size')
    df['SubIndustry_Count'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].transform('size')

    # Sort by 'Sector_Count', 'Industry_Count', 'SubIndustry_Count' in descending order
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], ascending=[False, False, False])

    # Rank based on 'Percentile' within each group defined by 'Sector', 'Industry', 'Sub-Industry'
    df['Rank'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].rank(ascending=False)

    # Final sort by the calculated 'Rank'
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count', 'Rank'], ascending=[False, False, False, True])

    # Drop auxiliary columns if not needed
    df.drop(columns=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], inplace=True)

    if filter == 'Y':
       # not to miss 30% :)
       df_filtered = df[ (
                       ((df['EPS Growth (TTM vs Prior TTM)'] >= 20)  | (df['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 20))
                       #&
                       #((df['Revenue Growth (TTM vs. Prior TTM)'] >= 0) | (df['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 0))
                       )]

       # not to miss revenue companies pre growth, lets say revenue > 30%

       df_filtered1 = df[(
                       ((df['EPS Growth (TTM vs Prior TTM)'].isna())  | (df['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'].isna()))
                       &
                       ((df['Revenue Growth (TTM vs. Prior TTM)'] >= 30) | (df['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 30))
                       )]
       # consistent growth
       #EPS Growth (3 Year History)	EPS Growth (5 Year Historical)	EPS Growth (Proj Next Yr vs. This Yr)
       df_filtered2 = df[(df['EPS Growth (Proj Next Yr vs. This Yr)'] > 50)]

       df = pd.concat([df_filtered, df_filtered1,df_filtered2], ignore_index=True)


    # atleast covered 50% off 52 week lows
    # Calculate 52-week low price
    df["52_Week_Low"] = df["Security Price"] * (1 - df["% Above 52 Week Low"] / 100)

      # Filter based on the condition
    df = df[df["Security Price"] >= df["52_Week_Low"] * 1.5]

    df = df[df["Earnings Announcements (Upcoming)"] > 5];

    df = df.drop_duplicates(subset='Symbol')

    return df



def chartAnalysis():

    # Use glob to find all files that start with "screener_results"
    file_list = glob.glob("screener_results*.xls")

    if not file_list:
        print("No files found with the pattern 'screener_results*.xls'")
        return

    df = pd.DataFrame()

    # Concatenate all files matching the pattern
    for file in file_list:
        df_temp = pd.read_excel(file)
        df = pd.concat([df, df_temp], ignore_index=True)

    # Drop duplicate rows based on the 'Symbol' column
    df = df.drop_duplicates(subset='Symbol')

    # add $volume to df, defined as Volume (10 Day Avg) in millions * df["Security Price"]

    df['$Volume'] = df["Volume (10 Day Avg)"]  * df["Security Price"]

    # Filter out rows where $Volume is less than 25
    df = df[df['$Volume'] >= 25]

    #print(df[['Symbol', '$Volume']].head(25))


    tickers = df["Symbol"].to_list()

    printFilteredData(tickers)

    # Read and filter CSV data for additional filtering
    # ... existing code ...

    rs_filtered_df = read_and_filter_csv(int(input("Enter RS score:")))

    df = df[df['Symbol'].isin(rs_filtered_df['Ticker'])]


    # Merge the additional columns into the screener dataframe
    df = df.merge(rs_filtered_df[['Ticker', 'Percentile']], left_on='Symbol', right_on='Ticker', how='left')


    filter = input("Earnings Filter needed(Y/N):")

    # Filter the screener DataFrame with CSV data
    screener = groupAndFilter(df, filter)

    printFilteredData( tickers = screener["Symbol"].to_list())



    # Drop the extra Ticker column if needed, since 'Symbol' and 'Ticker' are used for merging
    screener.drop(columns=['Ticker'], inplace=True)

    # done = ["TSM","APP"]
    # screener = screener[screener['Symbol'].isin(done)]

    #df_sorted = convergence(screener)

    df_sorted = screener

    tickers = df_sorted["Symbol"].to_list()

    printFilteredData(tickers)


    print("Start? (Y/N):")

    start = input()


    if start.strip().upper() == 'Y':
       df_sorted.to_excel("sorted_screener.xlsx")
       save_file = input('Save File True or False: ').strip().lower() == 'true'
       downloadChartsFidelityV2(df_sorted, "fidelity_screen", save_file)

chartAnalysis()
