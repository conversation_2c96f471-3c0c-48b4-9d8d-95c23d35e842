import csv
import time
from PIL import Image
import docx
import os
from common.http_session import get_session

headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36'}


def showImg():
    doc = docx.Document()
   # opening the CSV file path = 'src/data/'
    with open('src/data/urls.csv', mode='r')as file:

      # reading the CSV file
      csvFile = csv.reader(file)
      for idx, val in enumerate(csvFile):
         Image.open(str('src/data/images'+str(idx)) + '.png')
         doc.add_picture(str('src/data/images'+str(idx)) + '.png')
         #img.show()
    doc.save('market.docx')
    os.startfile('market.docx')


def dwnloadImg():
    # opening the CSV file path = 'src/data/'
    with open('src/data/urls.csv', mode='r')as file:

      # reading the CSV file
      csvFile = csv.reader(file)

      # displaying the contents of the CSV file
      for idx, val in enumerate(csvFile):
          with open(str('src/data/images'+str(idx)) + '.png', 'wb') as handle:
              url = val[0].replace('tdy', str(time.time()))
              time.sleep(2)

              # Create a session with proper SSL certificate handling
              session = get_session()

              # Make the request with our custom session
              response = session.get(url, headers=headers)
              if not response.ok:
                  print(response)
              for block in response.iter_content(1024):
                  if not block:
                      break
                  handle.write(block)

import webbrowser

def open_webpage(url):
    webbrowser.open(url)

# if __name__ == "__main__":
#     open_webpage("https://www.forexfactory.com/calendar?week=this")




# dwnloadImg()
# showImg()
# NAAIMExp()
#downloadCharts(['AMZN'])
