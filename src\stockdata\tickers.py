#!/usr/bin/env python

import ftplib
import re

# Connect to ftp.nasdaqtrader.com
ftp = ftplib.FTP('ftp.nasdaqtrader.com', 'anonymous', '<EMAIL>')

# Download files nasdaqlisted.txt and otherlisted.txt from ftp.nasdaqtrader.com
import os

# Create data directory if it doesn't exist
exchange_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'exchange')
os.makedirs(exchange_dir, exist_ok=True)

for ficheiro in ["nasdaqlisted.txt", "otherlisted.txt"]:
        ftp.cwd("/SymbolDirectory")
        file_path = os.path.join(exchange_dir, ficheiro)
        localfile = open(file_path, 'wb')
        ftp.retrbinary('RETR ' + ficheiro, localfile.write)
        localfile.close()
ftp.quit()

# Grep for common stock in nasdaqlisted.txt and otherlisted.txt
# Create tickers.txt file in the exchange directory
tickers_path = os.path.join(exchange_dir, "tickers.txt")

# Clear the file if it exists
open(tickers_path, "w").close()

for ficheiro in ["nasdaqlisted.txt", "otherlisted.txt"]:
        file_path = os.path.join(exchange_dir, ficheiro)
        localfile = open(file_path, 'r')
        for line in localfile:
                if re.search("Common Stock", line):
                        ticker = line.split("|")[0]
                        # Append tickers to file tickers.txt
                        open(tickers_path, "a+").write(ticker + "\n")