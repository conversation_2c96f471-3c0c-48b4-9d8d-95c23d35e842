# PythonicFin

A Python-based toolkit for financial analysis, trading automation, and market research.

> **Note**: Gmail-related functionality has been moved to a separate private repository called [gmail-cleanup](https://github.com/prasnna/gmail-cleanup).
>
> **Note**: MBTA-related functionality has been moved to a separate repository called [mbta-commute](https://github.com/prasnna/mbta-commute).

## Overview

PythonicFin is a collection of tools designed to automate various aspects of financial analysis and trading, including:

- Stock ticker analysis and visualization
- Position sizing calculations
- Technical indicators and chart studies
- Market screening and filtering
- Trade analysis and performance tracking
- Data collection from various financial APIs

## Features

- **Ticker Analysis**: Analyze stock tickers with technical indicators, volume analysis, and price patterns
- **Market Screening**: Filter stocks based on custom criteria
- **Position Sizing**: Calculate optimal position sizes based on risk parameters
- **Data Integration**: Connect to various financial data sources (Yahoo Finance, Robinhood, etc.)
- **Batch Processing**: Run automated analysis on multiple securities
- **Visualization**: Generate charts and visual representations of financial data

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Required Python packages (see `requirements.txt`)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/prasnna/pythonicfin.git
   cd pythonicfin
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up environment variables (see [ENV_SETUP.md](ENV_SETUP.md) for details):
   ```
   cp .env.sample .env
   # Edit .env with your credentials
   ```

### Usage

#### Ticker Analysis

Run the ticker analysis tool:
```
.\ticker.bat AAPL
```

This will analyze the AAPL ticker with default settings.

#### Market Screening

Run the market screener:
```
.\screener.bat
```

## Project Structure

- `src/`: Source code
  - `ticker_analyzer/`: Stock ticker analysis tools
  - `stockdata/`: Data retrieval and processing
  - `util/`: Utility functions and helpers
  - `tradeanalysis/`: Trade analysis and performance tracking
  - `fidelity/`: Fidelity integration
  - `bonds/`: Bond analysis tools

## Configuration

The application uses a combination of configuration files and environment variables:

- `src/ticker_analyzer/config.yaml`: Configuration for ticker analysis
- `.env`: Environment variables for API keys and credentials

See [ENV_SETUP.md](ENV_SETUP.md) for details on setting up environment variables.

## Known Issues and Fixes

### Yahoo Finance API

When using the `yfinance` library, you may encounter issues with data retrieval. This project requires a specific User-Agent header to work properly with the Yahoo Finance API:

```python
user_agent_headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2171.95 Safari/537.36'}
```

See [docs/yfinance_fix.md](docs/yfinance_fix.md) for more details on this issue and how to fix it.

## Repository Cleanup

This repository has been cleaned up to remove large binary files and executables that were previously tracked in Git. These files are now untracked but may still exist in your local working directory.

### Large Files Documentation

See [LARGE_FILES.md](LARGE_FILES.md) for a detailed explanation of what large files were previously tracked and why they were included.

### Cleanup Process

The cleanup process involved:

1. Documenting large files and their purpose
2. Untracking these files from Git without deleting them from the working directory
3. Updating .gitignore to prevent them from being re-added

To run the cleanup script on your local repository:

```powershell
.\untrack_large_files.ps1
```

### Large File Handling

For development, large binary dependencies should be:

- Downloaded directly from official sources
- Managed through package managers when possible
- Documented in the project requirements
- NOT committed to the Git repository

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
